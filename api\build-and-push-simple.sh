#!/bin/bash

# 设置错误时退出
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
REGISTRY="asia-southeast2-docker.pkg.dev"
PROJECT_ID="testgke-464505"
FRONT_IMAGE="genco-front"
ADMIN_IMAGE="genco-admin"
TAG="latest"

# 完整的镜像名称
FRONT_FULL_IMAGE="${REGISTRY}/${PROJECT_ID}/${FRONT_IMAGE}/${FRONT_IMAGE}:${TAG}"
ADMIN_FULL_IMAGE="${REGISTRY}/${PROJECT_ID}/${ADMIN_IMAGE}/${ADMIN_IMAGE}:${TAG}"

echo "=========================================="
echo "    Genco Docker 镜像构建和推送脚本"
echo "=========================================="
echo "项目ID: ${PROJECT_ID}"
echo "注册表: ${REGISTRY}"
echo "前端镜像: ${FRONT_FULL_IMAGE}"
echo "管理端镜像: ${ADMIN_FULL_IMAGE}"
echo "=========================================="

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    log_error "Docker 未运行，请先启动 Docker"
    exit 1
fi

# 步骤1: 构建镜像
log_info "开始构建 Docker 镜像..."
docker-compose build

if [ $? -eq 0 ]; then
    log_success "Docker 镜像构建完成"
else
    log_error "Docker 镜像构建失败"
    exit 1
fi

# 步骤2: 标记前端镜像
log_info "标记前端镜像..."
docker tag ${FRONT_IMAGE}:${TAG} ${FRONT_FULL_IMAGE}

if [ $? -eq 0 ]; then
    log_success "前端镜像标记完成: ${FRONT_FULL_IMAGE}"
else
    log_error "前端镜像标记失败"
    exit 1
fi

# 步骤3: 推送前端镜像
log_info "推送前端镜像到 Google Cloud Artifact Registry..."
docker push ${FRONT_FULL_IMAGE}

if [ $? -eq 0 ]; then
    log_success "前端镜像推送完成"
else
    log_error "前端镜像推送失败"
    exit 1
fi

# 步骤4: 标记管理端镜像
log_info "标记管理端镜像..."
docker tag ${ADMIN_IMAGE}:${TAG} ${ADMIN_FULL_IMAGE}

if [ $? -eq 0 ]; then
    log_success "管理端镜像标记完成: ${ADMIN_FULL_IMAGE}"
else
    log_error "管理端镜像标记失败"
    exit 1
fi

# 步骤5: 推送管理端镜像
log_info "推送管理端镜像到 Google Cloud Artifact Registry..."
docker push ${ADMIN_FULL_IMAGE}

if [ $? -eq 0 ]; then
    log_success "管理端镜像推送完成"
else
    log_error "管理端镜像推送失败"
    exit 1
fi

echo "=========================================="
log_success "所有镜像构建和推送完成！"
echo "=========================================="
echo "前端镜像: ${FRONT_FULL_IMAGE}"
echo "管理端镜像: ${ADMIN_FULL_IMAGE}"
echo "==========================================" 