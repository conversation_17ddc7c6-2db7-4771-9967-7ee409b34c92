package com.genco.service.service;

import com.genco.common.request.AffiliateProductSearchRequest;
import com.genco.common.response.AffiliateProductResponse;
import tiktokshop.open.sdk_java.invoke.ApiException;

/**
 * 联盟选品服务接口
 */
public interface AffiliateProductService {

    /**
     * 搜索联盟产品
     *
     * @param request 搜索请求参数
     * @return 搜索结果
     */
    AffiliateProductResponse searchProducts(AffiliateProductSearchRequest request) throws ApiException;
}
