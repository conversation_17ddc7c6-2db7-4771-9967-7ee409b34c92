<template>
  <div class="divBox relative">
    <el-card class="box-card">
      <el-tabs v-model="form.type" @tab-click="onChangeType" class="mb20">
        <el-tab-pane :label="$t('product.isHot')" name="1"></el-tab-pane>
        <el-tab-pane :label="$t('product.isBenefit')" name="2"></el-tab-pane>
        <el-tab-pane :label="$t('product.isTikTok')" name="3"></el-tab-pane>
      </el-tabs>
      <div class="container mt-1">
        <el-form v-model="form" inline size="small">
          <el-form-item :label="$t('product.productName')">
            <el-input
              v-model="form.keywords"
              :placeholder="$t('product.enterProductName')"
              clearable
            />
          </el-form-item>
          <el-form-item :label="$t('product.status')">
            <el-select
              v-model="form.isShow"
              :placeholder="$t('product.pleaseSelect')"
            >
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="$t('product.' + item.label)"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <el-button size="small" type="primary" class="mr10" @click="getList">
        {{ $t("product.query") }}
      </el-button>

      <el-button size="small" type="" class="mr10" @click="resetForm()">
        {{ $t("product.reset") }}
      </el-button>
    </el-card>

    <el-card class="box-card" style="margin-top: 12px;">
      <el-table
        v-loading="loading"
        :data="tableData"
        size="small"
        :header-cell-style="{ fontWeight: 'bold' }"
      >
        <el-table-column
          :label="$t('common.serialNumber')"
          type="index"
          width="110"
        />
        <el-table-column :label="$t('product.productImage')" min-width="80">
          <template slot-scope="scope">
            <div class="demo-image__preview">
              <el-image
                style="width: 36px; height: 36px"
                :src="scope.row.image"
                :preview-src-list="[scope.row.image]"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('product.productName')"
          prop="storeName"
          min-width="300"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          :label="$t('product.productPrice')"
          min-width="90"
          align="center"
        >
        <template slot-scope="scope">{{
            formatAmount(scope.row.price)
          }}</template>
        </el-table-column>
        <el-table-column
          :label="$t('product.cashbackRate')"
          min-width="80"
          align="center"
        >
          <template slot-scope="scope">{{
            formatRate(scope.row.cashBackRate)
          }}</template>
        </el-table-column>
        <el-table-column
          :label="$t('product.usercashbackRate')"
          min-width="100"
          align="center"
        >
          <template slot-scope="scope">{{
            formatRate(scope.row.userCashBackRate)
          }}</template>
        </el-table-column>
        <el-table-column
          :label="$t('product.addTime')"
          min-width="120"
          align="center"
          prop="addTime"
        >
          <template slot-scope="scope">{{
            formatTime(scope.row.addTime)
          }}</template>
        </el-table-column>
        <el-table-column
          :label="$t('product.action')"
          min-width="60"
          fixed="right"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handleDelete(scope.row)"
            >
              {{ $t("product.offline") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        class="mt20"
        @size-change="sizeChange"
        @current-change="pageChange"
        :current-page="form.page"
        :page-sizes="[20, 40, 60, 100]"
        :page-size="form.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="form.total"
      />
    </el-card>
  </div>
</template>
<script>
import { productLstApi, productUpdateApi } from "@/api/store";
import {
  batchPutOn,
  batchPutoff,
  updateProductInfo
} from "@/api/brand";


export default {
  name: "AppHome",
  data() {
    return {
      statusOptions: [
        { value: -1, label: this.$t("all") },
        { value: 1, label: this.$t("online") },
        { value: 0, label: this.$t("offline") }
      ],
      form: {
        keywords: "",
        page: 1,
        limit: 20,
        type: "1",
        isShow:""
      },
      tableData: [],
      levelList: [],
      levelData: [],
      loading: false
    };
  },
  created() {},
  mounted() {
    this.getList();
  },
  methods: {
    formatAmount(s){
        if(s == undefined) {
            s = 0
        }
        let s1 = (s/1000).toFixed(3)
        return s1
    },
    onChangeType() {
      this.getList();
    },
    // 列表
    getList(num) {
      this.form.page = 1;
      this.loading = true;
      productLstApi(this.form)
        .then(res => {
          this.tableData = res.list;
          this.form.total = res.total;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    //切换页数
    pageChange(index) {
      this.form.page = index;
      this.getList();
    },
    //切换显示条数
    sizeChange(index) {
      this.form.limit = index;
      this.getList();
    },
    resetForm() {
      this.form.keywords = "";
      this.form.page = 1;
      this.form.limit = 20;
      this.getList();
    },
    formatTime(t) {
      let date = new Date(t * 1000);
      let year = date.getFullYear();
      let month = String(date.getMonth() + 1).padStart(2, "0");
      let day = String(date.getDate()).padStart(2, "0");
      let hours = String(date.getHours()).padStart(2, "0");
      let minutes = String(date.getMinutes()).padStart(2, "0");
      let seconds = String(date.getSeconds()).padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    handleUpdate(row, type) {
      let _this = this;
      this.$confirm(
        this.$t("brand.confirmOperation"),
        this.$t("brand.prompt"),
        {
          confirmButtonText: this.$t("brand.confirm"),
          cancelButtonText: this.$t("brand.cancel"),
          type: "warning",
          showClose: false
        }
      ).then(() => {
        let params = { ids: [row.id] };
        if (row.isShow) {
          batchPutoff(params).then(res => {
            _this.getList();
          });
        } else {
          batchPutOn(params).then(res => {
            _this.getList();
          });
        }
      });
    },
    formatRate(s) {
      return parseInt(s * 10000) / 100 + "%";
    },
    handleDelete(row) {
      let _this = this;
      let item = { id: row.id };
      if(this.form.type == 1){
        item['isHot'] = false
      }else if(this.form.type == 2){
        item['isBenefit'] = false
      }else if(this.form.type == 3){
        item['isBest'] = false
      }
      this.$confirm(
        this.$t("brand.confirmOperation"),
        this.$t("brand.prompt"),
        {
          confirmButtonText: this.$t("brand.confirm"),
          cancelButtonText: this.$t("brand.cancel"),
          type: "warning",
          showClose: false
        }
      ).then(() => {
        _this.loading = true;
        updateProductInfo(item)
        .then(res => {
          _this.getList();
        })
        .catch(res => {});
      });
    }
  }
};
</script>
<style scoped lang="scss">
/**/
</style>
