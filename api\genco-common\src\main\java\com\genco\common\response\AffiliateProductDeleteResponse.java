package com.genco.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 联盟商品删除响应
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AffiliateProductDeleteResponse对象", description = "联盟商品删除响应")
public class AffiliateProductDeleteResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "总数量")
    private Integer totalCount;

    @ApiModelProperty(value = "成功数量")
    private Integer successCount;

    @ApiModelProperty(value = "失败数量")
    private Integer failedCount;

    @ApiModelProperty(value = "成功删除的商品ID列表")
    private List<String> successProductIds;

    @ApiModelProperty(value = "失败的商品详情")
    private List<FailedProductInfo> failedProducts;

    @Data
    @ApiModel(value = "FailedProductInfo", description = "失败商品信息")
    public static class FailedProductInfo {
        @ApiModelProperty(value = "商品ID")
        private String productId;

        @ApiModelProperty(value = "失败原因")
        private String errorMessage;
    }
}
