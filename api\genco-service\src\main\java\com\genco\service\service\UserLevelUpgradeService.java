package com.genco.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.genco.common.model.user.UserLevelUpgradeOrder;
import com.genco.common.request.PageParamRequest;
import com.genco.common.request.UserLevelUpgradeRequest;
import com.genco.common.response.UserLevelUpgradeResponse;

import java.util.List;

/**
 * 用户等级升级服务接口
 */
public interface UserLevelUpgradeService extends IService<UserLevelUpgradeOrder> {

    /**
     * 分页获取等级升级订单列表
     *
     * @param pageParamRequest 分页参数
     * @return 订单列表
     */
    List<UserLevelUpgradeOrder> getList(PageParamRequest pageParamRequest);

    /**
     * 用户购买等级升级
     *
     * @param request 升级请求
     * @return 升级结果
     */
    UserLevelUpgradeResponse purchaseUpgrade(UserLevelUpgradeRequest request);

    /**
     * 检查用户是否可以升级到指定等级
     *
     * @param uid 用户ID
     * @param toLevelId 目标等级ID
     * @return 是否可以升级
     */
    Boolean canUpgrade(Integer uid, Integer toLevelId);

    /**
     * 获取等级升级费用
     *
     * @param fromLevelId 原等级ID
     * @param toLevelId 目标等级ID
     * @return 升级费用
     */
    java.math.BigDecimal getUpgradePrice(Integer fromLevelId, Integer toLevelId);

    /**
     * 处理升级订单支付成功
     *
     * @param orderNo 订单号
     * @return 处理结果
     */
    Boolean handlePaymentSuccess(String orderNo);

    /**
     * 取消升级订单
     *
     * @param orderNo 订单号
     * @return 取消结果
     */
    Boolean cancelOrder(String orderNo);
}
