package com.genco.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用户等级升级响应
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "UserLevelUpgradeResponse对象", description = "用户等级升级响应")
public class UserLevelUpgradeResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "原等级名称")
    private String fromLevelName;

    @ApiModelProperty(value = "目标等级名称")
    private String toLevelName;

    @ApiModelProperty(value = "升级费用")
    private BigDecimal upgradePrice;

    @ApiModelProperty(value = "支付方式")
    private String paymentMethod;

    @ApiModelProperty(value = "订单状态：0-待支付，1-已支付，2-已取消，3-已退款")
    private Integer orderStatus;

    @ApiModelProperty(value = "是否升级成功")
    private Boolean success;

    @ApiModelProperty(value = "提示信息")
    private String message;
}
