package com.genco.service.service.impl;

import cn.hutool.core.util.StrUtil;
import com.genco.common.constants.SysConfigConstants;
import com.genco.common.exception.CrmebException;
import com.genco.common.request.AffiliateProductSearchRequest;
import com.genco.common.response.AffiliateProductResponse;
import com.genco.service.service.AffiliateProductService;
import com.genco.service.service.AffiliateProductHistoryService;
import com.genco.service.service.StoreProductService;
import com.genco.service.service.SystemConfigService;
import com.genco.common.model.product.StoreProduct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tiktokshop.open.sdk_java.api.AffiliateCreatorV202405Api;
import tiktokshop.open.sdk_java.invoke.ApiClient;
import tiktokshop.open.sdk_java.invoke.ApiException;
import tiktokshop.open.sdk_java.model.AffiliateCreator.V202405.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 联盟选品服务实现类
 */
@Slf4j
@Service
public class AffiliateProductServiceImpl implements AffiliateProductService {

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private AffiliateProductHistoryService affiliateProductHistoryService;

    @Autowired
    private StoreProductService storeProductService;

    @Override
    public AffiliateProductResponse searchProducts(AffiliateProductSearchRequest request) throws ApiException {
        log.info("开始联盟选品查询，请求参数: {}", request);

        // 创建API客户端
        ApiClient apiClient = createApiClient();
        AffiliateCreatorV202405Api apiInstance = new AffiliateCreatorV202405Api(apiClient);

        // 构建请求体
        CreatorSearchOpenCollaborationProductRequestBody requestBody = buildRequestBody(request);

        // 调用TikTok API
        log.info("调用TikTok联盟选品API，pageSize: {}, sortField: {}, sortOrder: {}",
                request.getPageSize(), request.getSortField(), request.getSortOrder());

        CreatorSearchOpenCollaborationProductResponse response = apiInstance
                .affiliateCreator202405OpenCollaborationsProductsSearchPost(
                        request.getPageSize(),
                        apiClient.getTokens(),
                        "application/json",
                        request.getCursor(),
                        request.getSortField(),
                        request.getSortOrder(),
                        requestBody
                );

        log.info("TikTok API调用成功，响应码: {}, 消息: {}", response.getCode(), response.getMessage());

        // 同步TikTok商品数据到历史记录表
        if (response.getData() != null && response.getData().getProducts() != null) {
            // 这里需要转换为CreatorSelectAffiliateProductResponseDataProducts类型
            // 由于API版本不同，我们需要在convertToResponse中处理同步逻辑
        }

        // 转换响应数据
        return convertToResponse(response);
    }

    /**
     * 创建API客户端
     */
    private ApiClient createApiClient() {
        // 获取配置信息
        String appKey = systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_APP_KEY);
        String appSecret = systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_APP_SECRET);
        String accessToken = systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_ACCESS_TOKEN);

        // 验证必要的配置
        if (StrUtil.isBlank(appKey)) {
            throw new CrmebException("TikTok App Key未配置");
        }
        if (StrUtil.isBlank(appSecret)) {
            throw new CrmebException("TikTok App Secret未配置");
        }
        if (StrUtil.isBlank(accessToken)) {
            throw new CrmebException("TikTok Access Token未配置");
        }

        // 创建API客户端
        ApiClient apiClient = new ApiClient();
        apiClient.setAppkey(appKey);
        apiClient.setSecret(appSecret);
        apiClient.setTokens(accessToken);
        apiClient.setBasePath("https://open-api.tiktokglobalshop.com");

        return apiClient;
    }

    /**
     * 构建请求体
     */
    private CreatorSearchOpenCollaborationProductRequestBody buildRequestBody(AffiliateProductSearchRequest request) {
        CreatorSearchOpenCollaborationProductRequestBody requestBody =
                new CreatorSearchOpenCollaborationProductRequestBody();

        // 设置标题关键词
        if (request.getTitleKeywords() != null && !request.getTitleKeywords().isEmpty()) {
            requestBody.setTitleKeywords(request.getTitleKeywords());
        }

        // 设置价格范围
        if (request.getSalesPriceMin() != null || request.getSalesPriceMax() != null) {
            CreatorSearchOpenCollaborationProductRequestBodySalesPriceRange priceRange =
                    new CreatorSearchOpenCollaborationProductRequestBodySalesPriceRange();

            if (request.getSalesPriceMin() != null) {
                priceRange.setAmountGe(request.getSalesPriceMin().toString());
            }
            if (request.getSalesPriceMax() != null) {
                priceRange.setAmountLt(request.getSalesPriceMax().toString());
            }

            requestBody.setSalesPriceRange(priceRange);
        }

        // 设置分类
        if (StrUtil.isNotBlank(request.getCategoryId())) {
            CreatorSearchOpenCollaborationProductRequestBodyCategory category =
                    new CreatorSearchOpenCollaborationProductRequestBodyCategory();
            category.setId(request.getCategoryId());
            requestBody.setCategory(category);
        }

        // 设置佣金率范围
        if (request.getCommissionRateMin() != null || request.getCommissionRateMax() != null) {
            CreatorSearchOpenCollaborationProductRequestBodyCommissionRateRange commissionRange =
                    new CreatorSearchOpenCollaborationProductRequestBodyCommissionRateRange();

            if (request.getCommissionRateMin() != null) {
                commissionRange.setRateGe(request.getCommissionRateMin());
            }
            if (request.getCommissionRateMax() != null) {
                commissionRange.setRateLt(request.getCommissionRateMax());
            }

            requestBody.setCommissionRateRange(commissionRange);
        }

        return requestBody;
    }

    /**
     * 转换响应数据
     */
    private AffiliateProductResponse convertToResponse(CreatorSearchOpenCollaborationProductResponse response) {
        AffiliateProductResponse result = new AffiliateProductResponse();

        if (response.getData() != null) {
            CreatorSearchOpenCollaborationProductResponseData data = response.getData();

            result.setNextPageToken(data.getNextPageToken());
            result.setTotalCount(data.getTotalCount());

            // 转换产品列表
            if (data.getProducts() != null) {
                List<AffiliateProductResponse.AffiliateProduct> products = data.getProducts().stream()
                        .map(this::convertProduct)
                        .collect(Collectors.toList());

                // 同步TikTok商品数据到历史记录表
                syncTikTokProductsToHistory(data.getProducts());

                // 批量查询已入库的商品，避免for循环查询数据库
                // 查询eb_store_product表的out_product_id字段，如果查出了数据，即表示已经导入
                List<String> productIds = products.stream()
                        .map(AffiliateProductResponse.AffiliateProduct::getId)
                        .collect(Collectors.toList());
                List<StoreProduct> existingProducts = storeProductService.getByOutProductIds(productIds);
                List<String> importedProductIds = existingProducts.stream()
                        .map(StoreProduct::getOutProductId)
                        .collect(Collectors.toList());

                // 设置isImported字段
                products.forEach(product -> {
                    product.setIsImported(importedProductIds.contains(product.getId()));
                });

                result.setProducts(products);
            } else {
                result.setProducts(new ArrayList<>());
            }
        }

        return result;
    }

    /**
     * 转换单个产品数据
     */
    private AffiliateProductResponse.AffiliateProduct convertProduct(
            CreatorSearchOpenCollaborationProductResponseDataProducts product) {

        AffiliateProductResponse.AffiliateProduct result = new AffiliateProductResponse.AffiliateProduct();

        result.setId(product.getId());
        result.setTitle(product.getTitle());
        result.setMainImageUrl(product.getMainImageUrl());
        result.setDetailLink(product.getDetailLink());
        result.setHasInventory(product.getHasInventory());

        // 安全转换Long到Integer
        Long unitsSold = product.getUnitsSold();
        if (unitsSold != null) {
            result.setUnitsSold(unitsSold.intValue());
        }
        result.setSaleRegion(product.getSaleRegion());

        // 转换价格信息
        if (product.getOriginalPrice() != null) {
            result.setOriginalPrice(convertPriceInfo(product.getOriginalPrice()));
        }
        if (product.getSalesPrice() != null) {
            result.setSalesPrice(convertPriceInfo(product.getSalesPrice()));
        }

        // 转换佣金信息
        if (product.getCommission() != null) {
            result.setCommission(convertCommissionInfo(product.getCommission()));
        }

        // 转换店铺信息
        if (product.getShop() != null) {
            AffiliateProductResponse.ShopInfo shopInfo = new AffiliateProductResponse.ShopInfo();
            CreatorSearchOpenCollaborationProductResponseDataProductsShop shop = product.getShop();
            if (shop != null) {
                shopInfo.setName(shop.getName());
            }
            result.setShop(shopInfo);
        }

        // 转换分类链信息
        List<CreatorSearchOpenCollaborationProductResponseDataProductsCategoryChains> categoryChains = product.getCategoryChains();
        if (categoryChains != null) {
            List<AffiliateProductResponse.CategoryInfo> categoryInfos = categoryChains.stream()
                    .map(this::convertCategoryInfo)
                    .collect(Collectors.toList());
            result.setCategoryChains(categoryInfos);
        }

        // 初始化isImported字段，将在convertToResponse方法中批量设置
        result.setIsImported(false);

        return result;
    }

    /**
     * 转换原价信息
     */
    private AffiliateProductResponse.PriceInfo convertPriceInfo(
            CreatorSearchOpenCollaborationProductResponseDataProductsOriginalPrice price) {
        AffiliateProductResponse.PriceInfo priceInfo = new AffiliateProductResponse.PriceInfo();
        priceInfo.setMinimumAmount(price.getMinimumAmount());
        priceInfo.setMaximumAmount(price.getMaximumAmount());
        priceInfo.setCurrency(price.getCurrency());
        return priceInfo;
    }

    /**
     * 转换销售价格信息
     */
    private AffiliateProductResponse.PriceInfo convertPriceInfo(
            CreatorSearchOpenCollaborationProductResponseDataProductsSalesPrice price) {
        AffiliateProductResponse.PriceInfo priceInfo = new AffiliateProductResponse.PriceInfo();
        priceInfo.setMinimumAmount(price.getMinimumAmount());
        priceInfo.setMaximumAmount(price.getMaximumAmount());
        priceInfo.setCurrency(price.getCurrency());
        return priceInfo;
    }

    /**
     * 转换佣金信息
     */
    private AffiliateProductResponse.CommissionInfo convertCommissionInfo(
            CreatorSearchOpenCollaborationProductResponseDataProductsCommission commission) {
        AffiliateProductResponse.CommissionInfo commissionInfo = new AffiliateProductResponse.CommissionInfo();
        commissionInfo.setRate(commission.getRate());
        commissionInfo.setAmount(commission.getAmount());
        commissionInfo.setCurrency(commission.getCurrency());

        return commissionInfo;
    }

    /**
     * 转换分类信息
     */
    private AffiliateProductResponse.CategoryInfo convertCategoryInfo(
            CreatorSearchOpenCollaborationProductResponseDataProductsCategoryChains categoryChain) {

        AffiliateProductResponse.CategoryInfo categoryInfo = new AffiliateProductResponse.CategoryInfo();
        categoryInfo.setId(categoryChain.getId());
        categoryInfo.setLocalName(categoryChain.getLocalName());
        categoryInfo.setParentId(categoryChain.getParentId());
        categoryInfo.setIsLeaf(categoryChain.getIsLeaf());

        return categoryInfo;
    }

    /**
     * 同步TikTok商品数据到历史记录表
     * @param tikTokProducts TikTok商品列表
     */
    private void syncTikTokProductsToHistory(List<CreatorSearchOpenCollaborationProductResponseDataProducts> tikTokProducts) {
        try {
            log.info("开始同步TikTok商品数据到历史记录表，商品数量：{}", tikTokProducts.size());

            for (CreatorSearchOpenCollaborationProductResponseDataProducts product : tikTokProducts) {
                try {
                    // 检查商品是否已存在历史记录
                    boolean exists = affiliateProductHistoryService.checkExistingProducts(
                            Arrays.asList(product.getId())).size() > 0;

                    if (!exists) {
                        // 创建新的历史记录（状态为未入库）
                        affiliateProductHistoryService.createProductHistoryFromTikTokData(product);
                        log.debug("创建TikTok商品历史记录：{}", product.getId());
                    } else {
                        // 更新现有记录的TikTok数据
                        affiliateProductHistoryService.updateProductHistoryFromTikTokData(product);
                        log.debug("更新TikTok商品历史记录：{}", product.getId());
                    }
                } catch (Exception e) {
                    log.error("同步单个TikTok商品数据失败，商品ID：{}，错误：{}", product.getId(), e.getMessage(), e);
                }
            }

            log.info("TikTok商品数据同步完成");
        } catch (Exception e) {
            log.error("同步TikTok商品数据到历史记录表失败", e);
        }
    }

    /**
     * 掩码敏感信息
     */
    private String maskSensitiveInfo(String info) {
        if (StrUtil.isBlank(info) || info.length() <= 8) {
            return "****";
        }
        return info.substring(0, 4) + "****" + info.substring(info.length() - 4);
    }
}
