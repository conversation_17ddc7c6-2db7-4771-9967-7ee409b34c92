package com.genco.common.model.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户等级升级订单表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("eb_user_level_upgrade_order")
@ApiModel(value = "UserLevelUpgradeOrder对象", description = "用户等级升级订单表")
public class UserLevelUpgradeOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "用户ID")
    private Integer uid;

    @ApiModelProperty(value = "原等级ID")
    private Integer fromLevelId;

    @ApiModelProperty(value = "目标等级ID")
    private Integer toLevelId;

    @ApiModelProperty(value = "升级费用")
    private BigDecimal upgradePrice;

    @ApiModelProperty(value = "支付方式：balance-余额支付")
    private String paymentMethod;

    @ApiModelProperty(value = "订单状态：0-待支付，1-已支付，2-已取消，3-已退款")
    private Integer orderStatus;

    @ApiModelProperty(value = "支付时间")
    private Date payTime;

    @ApiModelProperty(value = "取消时间")
    private Date cancelTime;

    @ApiModelProperty(value = "退款时间")
    private Date refundTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    // 订单状态常量
    public static final int STATUS_PENDING = 0;  // 待支付
    public static final int STATUS_PAID = 1;     // 已支付
    public static final int STATUS_CANCELLED = 2; // 已取消
    public static final int STATUS_REFUNDED = 3;  // 已退款

    // 支付方式常量
    public static final String PAYMENT_BALANCE = "balance"; // 余额支付
}
