{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:32.395",
                    "level": "INFO",
                    "thread": "main",
                    "class": "com.genco.admin.GencoAdminApplication",
                    "message": "Starting GencoAdminApplication on LAPTOP-3I7P6FTL with PID 23280 (C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\target\classes started by 吴兴龙 in C:\Users\<USER>\Desktop\新建文件夹 (3)\api)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:32.399",
                    "level": "INFO",
                    "thread": "main",
                    "class": "com.genco.admin.GencoAdminApplication",
                    "message": "The following profiles are active: prod" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:33.760",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.d.r.c.RepositoryConfigurationDelegate",
                    "message": "Multiple Spring Data modules found, entering strict repository configuration mode!" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:33.763",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.d.r.c.RepositoryConfigurationDelegate",
                    "message": "Bootstrapping Spring Data Redis repositories in DEFAULT mode." }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:33.809",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.d.r.c.RepositoryConfigurationDelegate",
                    "message": "Finished Spring Data repository scanning in 32ms. Found 0 Redis repository interfaces." }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:34.721",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker",
                    "message": "Bean 'org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration' of type [org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:34.728",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker",
                    "message": "Bean 'objectPostProcessor' of type [org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:34.731",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker",
                    "message": "Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@3b6c2be6' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:34.733",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker",
                    "message": "Bean 'org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration' of type [org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:34.740",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker",
                    "message": "Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:35.075",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.b.w.embedded.tomcat.TomcatWebServer",
                    "message": "Tomcat initialized with port(s): 20000 (http)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:35.087",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.apache.coyote.http11.Http11NioProtocol",
                    "message": "Initializing ProtocolHandler ["http-nio-20000"]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:35.088",
                    "level": "INFO",
                    "thread": "main",
                    "class": "org.apache.catalina.core.StandardService",
                    "message": "Starting service [Tomcat]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:35.088",
                    "level": "INFO",
                    "thread": "main",
                    "class": "org.apache.catalina.core.StandardEngine",
                    "message": "Starting Servlet engine: [Apache Tomcat/9.0.33]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:35.341",
                    "level": "INFO",
                    "thread": "main",
                    "class": "org.apache.jasper.servlet.TldScanner",
                    "message": "At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time." }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:35.348",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.a.c.c.C.[Tomcat].[localhost].[/]",
                    "message": "Initializing Spring embedded WebApplicationContext" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:35.348",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.web.context.ContextLoader",
                    "message": "Root WebApplicationContext: initialization completed in 2896 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:40.900",
                    "level": "INFO",
                    "thread": "main",
                    "class": "c.g.s.s.impl.PaymentStrategyFactory",
                    "message": "支付策略初始化完成，支持的支付类型: [offline, alipay, weixin, xendit, yue, haipay]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:42.240",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.b.a.e.web.EndpointLinksResolver",
                    "message": "Exposing 2 endpoint(s) beneath base path '/actuator'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:42.402",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.PropertySourcedRequestMappingHandlerMapping",
                    "message": "Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:42.450",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.s.concurrent.ThreadPoolTaskExecutor",
                    "message": "Initializing ExecutorService" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:42.450",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.s.concurrent.ThreadPoolTaskExecutor",
                    "message": "Initializing ExecutorService 'taskExecutor'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:42.744",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.s.web.DefaultSecurityFilterChain",
                    "message": "Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@38ae69f7, org.springframework.security.web.context.SecurityContextPersistenceFilter@51d76ad3, org.springframework.security.web.header.HeaderWriterFilter@2351255a, org.springframework.web.filter.CorsFilter@65ff4b8c, org.springframework.web.filter.CorsFilter@65ff4b8c, org.springframework.web.filter.CorsFilter@65ff4b8c, org.springframework.security.web.authentication.logout.LogoutFilter@57063e08, com.genco.admin.filter.JwtAuthenticationTokenFilter@2361365c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1bca9b64, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7ec176da, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@be9bf2e, org.springframework.security.web.session.SessionManagementFilter@45cddfd3, org.springframework.security.web.access.ExceptionTranslationFilter@4a7c8cb7, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@56017274]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:43.797",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.p.DocumentationPluginsBootstrapper",
                    "message": "Context refreshed" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:43.839",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.p.DocumentationPluginsBootstrapper",
                    "message": "Found 2 custom documentation plugin(s)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:43.960",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.s.ApiListingReferenceScanner",
                    "message": "Scanning for api listing references" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.474",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.480",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getByIdsUsingGET_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.487",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.499",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.511",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.513",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.543",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_2" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.545",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_2" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.570",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_2" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.574",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_3" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.592",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_3" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.622",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updatePhoneUsingPOST_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.630",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_4" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.649",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_2" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.654",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_5" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.656",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_3" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.660",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_2" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.661",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_4" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.662",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_6" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.664",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_7" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.667",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_3" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.698",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_8" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.718",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_4" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.733",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_3" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.734",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_5" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.761",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_4" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.784",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_9" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.788",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_5" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.794",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_4" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.795",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_6" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.796",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.817",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_10" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.823",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_5" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.824",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_2" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.836",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_11" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.841",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_5" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.861",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_12" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.894",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_6" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.917",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_13" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.934",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_6" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.969",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_14" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.987",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_7" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.998",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_6" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:44.998",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_7" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.003",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_7" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.013",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_15" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.015",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_8" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.017",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_7" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.019",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_8" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.024",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_16" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.025",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_9" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.028",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_8" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.028",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_8" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.032",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_17" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.033",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_9" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.053",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_18" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.054",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_10" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.059",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_9" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.060",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_9" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.060",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_3" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.061",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_10" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.066",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_19" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.067",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_11" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.072",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_10" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.073",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_10" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.073",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_4" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.074",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_11" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.080",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_20" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.084",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_12" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.088",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_11" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.093",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_11" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.097",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_12" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.099",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_21" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.100",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_13" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.102",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_12" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.102",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_12" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.110",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_22" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.116",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListTreeUsingGET_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.118",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_14" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.122",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_13" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.123",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_5" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.130",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_15" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.147",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_23" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.149",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_16" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.152",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_13" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.153",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_14" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.155",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_13" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.161",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_24" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.162",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_17" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.167",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_14" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.168",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_15" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.169",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_14" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.177",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_25" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.178",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_18" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.182",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_15" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.183",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_16" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.190",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingPOST_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.202",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_26" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.203",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_19" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.204",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_17" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.211",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_27" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.219",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_20" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.224",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_18" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.227",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_15" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.233",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_28" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.238",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_21" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.242",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_16" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.242",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_19" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.243",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingGET_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.244",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_16" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.251",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_29" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.253",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_22" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.260",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_17" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.261",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_20" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.262",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingGET_2" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.265",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_17" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.271",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_30" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.273",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_23" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.277",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_18" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.278",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_21" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.278",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingGET_3" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.279",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingPOST_2" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.281",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_31" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.285",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_19" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.286",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_22" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.348",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_32" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.349",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_24" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.369",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_23" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.387",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_33" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.397",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_24" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.398",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_6" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.400",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_18" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.402",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_34" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.403",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_25" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.406",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_20" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.408",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_25" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.416",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingPOST_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.419",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_35" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.420",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: balanceUsingPOST_1" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.431",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_36" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.433",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_19" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.434",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_37" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.435",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_26" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.437",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_21" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.438",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_26" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.471",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.s.ApiListingReferenceScanner",
                    "message": "Scanning for api listing references" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.489",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.s.concurrent.ThreadPoolTaskScheduler",
                    "message": "Initializing ExecutorService" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.501",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-3",
                    "class": "c.g.a.task.tiktok.TiktokTokenRefreshTask",
                    "message": "---TiktokTokenRefreshTask task------produce Data with fixed rate task: Execution Time - Mon Aug 04 09:17:45 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.505",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.apache.coyote.http11.Http11NioProtocol",
                    "message": "Starting ProtocolHandler ["http-nio-20000"]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.794",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.b.w.embedded.tomcat.TomcatWebServer",
                    "message": "Tomcat started on port(s): 20000 (http) with context path ''" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:45.805",
                    "level": "INFO",
                    "thread": "main",
                    "class": "com.genco.admin.GencoAdminApplication",
                    "message": "Started GencoAdminApplication in 14.074 seconds (JVM running for 14.953)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:46.169",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-3",
                    "class": "com.alibaba.druid.pool.DruidDataSource",
                    "message": "{dataSource-1} inited" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:46.234",
                    "level": "INFO",
                    "thread": "RMI TCP Connection(7)-************",
                    "class": "o.a.c.c.C.[Tomcat].[localhost].[/]",
                    "message": "Initializing Spring DispatcherServlet 'dispatcherServlet'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:46.235",
                    "level": "INFO",
                    "thread": "RMI TCP Connection(7)-************",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Initializing Servlet 'dispatcherServlet'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:46.254",
                    "level": "INFO",
                    "thread": "RMI TCP Connection(7)-************",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed initialization in 19 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:47.588",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-3",
                    "class": "c.g.a.task.tiktok.TiktokTokenRefreshTask",
                    "message": "Tiktok access_token 未到刷新时间，当前时间: 1754270265，过期时间: 1781838424" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:48.018",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Fri Aug 01 09:17:34 CST 2025, endTime: Mon Aug 04 09:17:34 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:50.009",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:51.535",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "API调用成功，获取到下一页token: , 开始处理订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:51.633",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始处理 0 个订单" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:51.633",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:51.783",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步事务执行完成，下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:51.783",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步方法执行完成，返回下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:58.437",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:58.437",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:58.437",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:58.437",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:58.613",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:58.613",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:58.613",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:58.613",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:59.410",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.ValidateCodeController.get()，prams：[]，cost time：672800900 ns，cost：672 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:59.410",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.ValidateCodeController.get()，prams：[]，cost time：672822000 ns，cost：672 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:59.445",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:59.445",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:59.446",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:59.446",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:59.620",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getLoginPic()，prams：[]，cost time：891263400 ns，cost：891 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:59.620",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getLoginPic()，prams：[]，cost time：891260400 ns，cost：891 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:59.621",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:59.621",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:59.621",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:59.621",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:07.091",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-26",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:07.095",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-26",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.104",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-26",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.SystemAdminLogin(SystemAdminLoginRequest,HttpServletRequest)，prams：[SystemAdminLoginRequest(account=admin, pwd=BzX36YjvNR8o, key=aa315f6647a490ff2df6059d5431ff20, code=2Jnq), SecurityContextHolderAwareRequestWrapper[ org.springframework.security.web.header.HeaderWriterFilter$HeaderWriterRequest@13c66d0f]]，cost time：********** ns，cost：1453 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.112",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-26",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.113",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-26",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.196",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.196",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.196",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.196",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.262",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：58044100 ns，cost：58 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.262",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.270",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.279",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.279",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.381",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemStoreStaffController.getList(Integer,PageParamRequest)，prams：[0, PageParamRequest(page=1, limit=9999)]，cost time：118203800 ns，cost：118 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.388",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.388",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.394",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：113252300 ns，cost：113 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.399",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.400",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.492",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.492",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.492",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.492",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.495",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-21",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.496",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-21",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.582",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-21",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：81225700 ns，cost：81 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.589",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-21",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.589",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-21",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.589",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：96412200 ns，cost：96 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.589",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.589",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.754",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.info(Integer)，prams：[100]，cost time：260464700 ns，cost：260 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.754",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.754",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:16.292",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:16.292",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:16.292",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:16.292",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:16.829",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemGroupDataController.getList(SystemGroupDataSearchRequest,PageParamRequest)，prams：[SystemGroupDataSearchRequest(keywords=null, gid=74, status=null), PageParamRequest(page=1, limit=20)]，cost time：523880100 ns，cost：523 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:16.837",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:16.837",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:18.178",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.StoreBrandController.getBrandList(Integer,String,PageParamRequest)，prams：[-1, , PageParamRequest(page=1, limit=20)]，cost time：1874175600 ns，cost：1874 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:18.190",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:18.190",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:29.850",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:29.850",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:30.086",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemMenuController.getList(SystemMenuSearchRequest)，prams：[SystemMenuSearchRequest(name=, menuType=)]，cost time：217630400 ns，cost：217 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:30.089",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:30.089",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:52.365",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Fri Aug 01 09:18:52 CST 2025 至 Mon Aug 04 09:18:52 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:19:52.782",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Fri Aug 01 09:19:35 CST 2025, endTime: Mon Aug 04 09:19:35 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:19:53.024",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:19:53.323",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "API调用成功，获取到下一页token: , 开始处理订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:19:53.388",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始处理 0 个订单" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:19:53.388",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:19:53.555",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步事务执行完成，下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:19:53.555",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步方法执行完成，返回下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:20:54.135",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-8",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Fri Aug 01 09:20:53 CST 2025 至 Mon Aug 04 09:20:53 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:21:22.552",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:21:22.553",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:21:22.654",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemMenuController.info(Integer)，prams：[429]，cost time：84755900 ns，cost：84 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:21:22.659",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:21:22.659",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:21:54.547",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Fri Aug 01 09:21:35 CST 2025, endTime: Mon Aug 04 09:21:35 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:21:54.783",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:21:54.993",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "API调用成功，获取到下一页token: , 开始处理订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:21:55.070",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始处理 0 个订单" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:21:55.070",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:21:55.221",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步事务执行完成，下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:21:55.227",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步方法执行完成，返回下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:22:36.102",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:22:36.102",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:22:44.405",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.StoreProductController.getList(StoreProductSearchRequest,PageParamRequest)，prams：[StoreProductSearchRequest(type=1, cateId=null, keywords=, isShow=null, brand=null), PageParamRequest(page=1, limit=20)]，cost time：8296518500 ns，cost：8296 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:22:44.455",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:22:44.455",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:22:55.799",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-11",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Fri Aug 01 09:22:55 CST 2025 至 Mon Aug 04 09:22:55 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:12.190",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:12.190",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:12.272",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.info(Integer)，prams：[100]，cost time：77439300 ns，cost：77 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:12.274",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:12.274",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:18.024",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-8",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:18.024",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:18.024",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-8",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:18.024",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:18.205",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemGroupDataController.getList(SystemGroupDataSearchRequest,PageParamRequest)，prams：[SystemGroupDataSearchRequest(keywords=null, gid=74, status=null), PageParamRequest(page=1, limit=20)]，cost time：181780100 ns，cost：181 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:18.205",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:18.205",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:23.922",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-8",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.StoreBrandController.getBrandList(Integer,String,PageParamRequest)，prams：[-1, , PageParamRequest(page=1, limit=20)]，cost time：5901551000 ns，cost：5901 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:23.931",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-8",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:23.931",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-8",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:26.856",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:26.856",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:26.856",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:26.857",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:31.263",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.StoreBrandController.getBrandList(Integer,String,PageParamRequest)，prams：[-1, , PageParamRequest(page=1, limit=10000)]，cost time：4410337400 ns，cost：4410 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:31.275",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:31.275",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:47.847",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.StoreProductController.getList(StoreProductSearchRequest,PageParamRequest)，prams：[StoreProductSearchRequest(type=0, cateId=null, keywords=, isShow=null, brand=), PageParamRequest(page=1, limit=20)]，cost time：20990130199 ns，cost：20990 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:47.847",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:47.847",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:54.562",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:54.562",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:56.192",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Fri Aug 01 09:23:36 CST 2025, endTime: Mon Aug 04 09:23:36 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:56.434",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:56.734",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "API调用成功，获取到下一页token: , 开始处理订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:56.817",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始处理 0 个订单" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:56.817",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:56.967",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步事务执行完成，下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:56.967",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步方法执行完成，返回下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:24:21.350",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.StoreProductController.getList(StoreProductSearchRequest,PageParamRequest)，prams：[StoreProductSearchRequest(type=0, cateId=null, keywords=, isShow=null, brand=), PageParamRequest(page=2, limit=20)]，cost time：26785143700 ns，cost：26785 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:24:21.350",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:24:21.350",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:24:35.116",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:24:35.116",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:24:57.526",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-4",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Fri Aug 01 09:24:57 CST 2025 至 Mon Aug 04 09:24:57 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:01.658",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.StoreProductController.getList(StoreProductSearchRequest,PageParamRequest)，prams：[StoreProductSearchRequest(type=1, cateId=null, keywords=, isShow=null, brand=null), PageParamRequest(page=1, limit=20)]，cost time：26546530499 ns，cost：26546 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:01.670",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:01.670",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:27.500",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:27.500",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:27.500",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:27.500",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:27.675",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.StoreProductShareRecordController.getList(StoreProductShareRecordSearchRequest,PageParamRequest)，prams：[StoreProductShareRecordSearchRequest(userId=null, tiktokUid=null, userAccount=null, productId=null, productName=null, channel=null, operateTimeStart=null, operateTimeEnd=null), PageParamRequest(page=1, limit=20)]，cost time：********* ns，cost：172 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:27.685",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:27.685",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-27",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:35.353",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.StoreBrandController.getBrandList(Integer,String,PageParamRequest)，prams：[-1, , PageParamRequest(page=1, limit=999999)]，cost time：7851599300 ns，cost：7851 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:35.356",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:35.356",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-10",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:39.237",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:39.238",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:39.238",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:39.238",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:39.341",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.UserExtractController.getList(UserExtractSearchRequest,PageParamRequest)，prams：[UserExtractSearchRequest(keywords=, extractType=wallet, status=0, bankCode=null, bankName=, walletCode=, walletAccount=null, dateLimit=), PageParamRequest(page=1, limit=20)]，cost time：******** ns，cost：99 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:39.341",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:39.341",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:39.500",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.UserExtractController.getExtractBank()，prams：[]，cost time：********* ns，cost：263 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:39.500",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:39.500",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:41.730",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:41.730",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:41.731",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:41.731",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:41.991",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.UserExtractController.getExtractBank()，prams：[]，cost time：********* ns，cost：259 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:41.991",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:41.991",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:42.008",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.UserExtractController.getList(UserExtractSearchRequest,PageParamRequest)，prams：[UserExtractSearchRequest(keywords=, extractType=wallet, status=null, bankCode=null, bankName=, walletCode=, walletAccount=null, dateLimit=), PageParamRequest(page=1, limit=20)]，cost time：********* ns，cost：282 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:42.016",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:42.016",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:52.976",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:52.977",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:53.533",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.StoreOrderController.getList(StoreOrderSearchRequest,PageParamRequest)，prams：[StoreOrderSearchRequest(orderNo=, productTitle=, dateLimit=, status=null, type=2), PageParamRequest(page=1, limit=20)]，cost time：495669701 ns，cost：495 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:53.541",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:53.541",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:57.928",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-16",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Fri Aug 01 09:25:36 CST 2025, endTime: Mon Aug 04 09:25:36 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:58.163",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-16",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:58.392",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-16",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "API调用成功，获取到下一页token: , 开始处理订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:58.468",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-16",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始处理 0 个订单" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:58.468",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-16",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:58.620",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-16",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步事务执行完成，下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:58.620",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-16",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步方法执行完成，返回下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.555",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-26",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.555",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-26",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.555",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-26",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：53200 ns，cost：0 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.558",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-26",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.558",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-26",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.620",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.620",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.701",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：80774200 ns，cost：80 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.705",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.705",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.788",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.788",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.788",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.788",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.788",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.788",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.866",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：77883801 ns，cost：77 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.866",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.866",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-16",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:11.033",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：255788500 ns，cost：255 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:11.048",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:11.048",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:11.317",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.StoreOrderController.getList(StoreOrderSearchRequest,PageParamRequest)，prams：[StoreOrderSearchRequest(orderNo=, productTitle=, dateLimit=, status=null, type=2), PageParamRequest(page=1, limit=20)]，cost time：524177900 ns，cost：524 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:11.322",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:11.322",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:21.079",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-21",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:21.080",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-21",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:21.183",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-21",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemUserLevelController.getList()，prams：[]，cost time：105783200 ns，cost：105 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:21.191",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-21",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:21.191",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-21",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:33.701",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:33.701",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:33.784",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemUserLevelController.getList()，prams：[]，cost time：86128400 ns，cost：86 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:33.790",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:33.790",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-22",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:33.975",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:33.975",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:34.417",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.UserController.getList(UserSearchRequest,PageParamRequest)，prams：[UserSearchRequest(keywords=, phone=null, nickname=null, dateLimit=null, groupId=null, labelId=null, userType=null, status=null, isPromoter=null, payCount=null, level=, accessType=0, country=null, province=null, city=null, sex=null), PageParamRequest(page=1, limit=20)]，cost time：436889400 ns，cost：436 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:34.432",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:34.432",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:53.688",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:53.688",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:53.688",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:53.688",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:53.774",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.UserExtractController.getExtractBank()，prams：[]，cost time：******** ns，cost：86 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:53.774",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:53.774",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:54.490",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.UserRechargeController.getList(UserRechargeSearchRequest,PageParamRequest)，prams：[UserRechargeSearchRequest(keywords=null, dateLimit=, uid=null, rechargeType=, payChannel=), PageParamRequest(page=1, limit=20)]，cost time：797681000 ns，cost：797 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:54.495",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:54.495",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:00.079",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-9",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Fri Aug 01 09:26:59 CST 2025 至 Mon Aug 04 09:26:59 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:05.600",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:05.600",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:05.601",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:05.601",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:05.854",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.UserExtractController.getExtractBank()，prams：[]，cost time：********* ns，cost：253 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:05.858",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:05.858",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:05.860",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.UserExtractController.getList(UserExtractSearchRequest,PageParamRequest)，prams：[UserExtractSearchRequest(keywords=, extractType=wallet, status=null, bankCode=null, bankName=, walletCode=, walletAccount=null, dateLimit=), PageParamRequest(page=1, limit=20)]，cost time：********* ns，cost：257 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:05.861",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:05.861",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:24.062",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:24.062",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:24.062",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:24.062",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:24.149",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.UserExtractController.getExtractBank()，prams：[]，cost time：******** ns，cost：86 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:24.149",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:24.149",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-6",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:24.831",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.UserExtractController.getList(UserExtractSearchRequest,PageParamRequest)，prams：[UserExtractSearchRequest(keywords=, extractType=wallet, status=null, bankCode=null, bankName=, walletCode=, walletAccount=null, dateLimit=), PageParamRequest(page=1, limit=20)]，cost time：********* ns，cost：766 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:24.831",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:24.831",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:32.033",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:32.033",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:32.286",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.info(Integer)，prams：[111]，cost time：254913000 ns，cost：254 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:32.294",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:32.294",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:32.302",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:32.302",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:32.569",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.info(Integer)，prams：[112]，cost time：260844600 ns，cost：260 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:32.569",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:32.569",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:39.517",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-8",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:39.517",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-8",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:39.773",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-8",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.info(Integer)，prams：[113]，cost time：255926800 ns，cost：255 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:39.773",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-8",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:39.773",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-8",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:39.791",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:39.791",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:40.040",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.info(Integer)，prams：[114]，cost time：252317100 ns，cost：252 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:40.040",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:40.048",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:40.060",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:40.062",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:40.315",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.info(Integer)，prams：[115]，cost time：252864100 ns，cost：252 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:40.315",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:40.315",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:47.589",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.g.a.task.tiktok.TiktokTokenRefreshTask",
                    "message": "---TiktokTokenRefreshTask task------produce Data with fixed rate task: Execution Time - Mon Aug 04 09:27:47 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:47.844",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.g.a.task.tiktok.TiktokTokenRefreshTask",
                    "message": "Tiktok access_token 未到刷新时间，当前时间: 1754270867，过期时间: 1781838424" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.076",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.077",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.078",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：90500 ns，cost：0 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.081",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.081",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.092",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.093",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.336",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：257322000 ns，cost：257 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.352",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.352",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.443",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.443",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.443",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.443",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.444",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.444",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.527",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.info(Integer)，prams：[113]，cost time：85390400 ns，cost：85 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.527",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.527",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.604",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：160137600 ns，cost：160 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.604",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.604",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.694",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：252890100 ns，cost：252 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.694",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.694",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:54.986",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:54.986",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:55.239",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.info(Integer)，prams：[113]，cost time：252880500 ns，cost：252 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:55.240",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:55.240",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:01.380",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-10",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Fri Aug 01 09:27:37 CST 2025, endTime: Mon Aug 04 09:27:37 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:02.143",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-10",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:02.455",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-10",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "API调用成功，获取到下一页token: , 开始处理订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:02.748",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-10",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始处理 0 个订单" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:02.749",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-10",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:03.251",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-10",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步事务执行完成，下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:03.251",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-10",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步方法执行完成，返回下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:08.207",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:08.208",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:08.458",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.info(Integer)，prams：[103]，cost time：251534400 ns，cost：251 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:08.458",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:08.458",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-11",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:14.847",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:14.848",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:15.101",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.info(Integer)，prams：[104]，cost time：254916000 ns，cost：254 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:15.101",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:15.101",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:38.098",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:38.098",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:38.352",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.info(Integer)，prams：[144]，cost time：255818900 ns，cost：255 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:38.361",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:38.361",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-7",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:05.043",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-21",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Fri Aug 01 09:29:04 CST 2025 至 Mon Aug 04 09:29:04 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:07.541",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:07.543",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:08.074",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemRoleController.getList(SystemRoleSearchRequest,PageParamRequest)，prams：[SystemRoleSearchRequest(roleName=null, status=null), PageParamRequest(page=1, limit=20)]，cost time：526369300 ns，cost：526 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:08.080",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:08.080",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:28.443",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:28.443",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:28.443",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:28.443",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:28.782",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemAdminController.getList(SystemAdminRequest,PageParamRequest)，prams：[SystemAdminRequest(realName=null, roles=null, status=null), PageParamRequest(page=1, limit=20)]，cost time：336394200 ns，cost：336 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:28.785",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:28.785",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:28.951",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemRoleController.getList(SystemRoleSearchRequest,PageParamRequest)，prams：[SystemRoleSearchRequest(roleName=null, status=null), PageParamRequest(page=1, limit=100)]，cost time：506621600 ns，cost：506 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:28.954",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:28.954",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-13",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:41.758",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:41.758",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:42.778",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemMenuController.getList(SystemMenuSearchRequest)，prams：[SystemMenuSearchRequest(name=, menuType=)]，cost time：1017995400 ns，cost：1017 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:42.783",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:42.783",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-18",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.036",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.036",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.037",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：97200 ns，cost：0 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.040",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.040",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-20",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.065",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.065",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.324",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：258444800 ns，cost：258 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.326",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.327",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.403",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.403",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.403",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.403",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.403",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.403",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.482",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：77337500 ns，cost：77 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.484",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.485",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-29",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.658",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：253632100 ns，cost：253 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.661",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.661",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-25",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.675",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemMenuController.getList(SystemMenuSearchRequest)，prams：[SystemMenuSearchRequest(name=, menuType=)]，cost time：269483600 ns，cost：269 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.715",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.715",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-30",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:30:05.495",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-6",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Fri Aug 01 09:29:37 CST 2025, endTime: Mon Aug 04 09:29:37 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:30:05.757",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-6",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:30:06.371",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-6",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "API调用成功，获取到下一页token: , 开始处理订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:30:06.455",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-6",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始处理 0 个订单" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:30:06.455",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-6",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:30:06.625",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-6",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步事务执行完成，下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:30:06.625",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-6",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步方法执行完成，返回下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:31:07.250",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-24",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Fri Aug 01 09:31:07 CST 2025 至 Mon Aug 04 09:31:07 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:32:07.705",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Fri Aug 01 09:31:38 CST 2025, endTime: Mon Aug 04 09:31:38 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:32:07.970",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:32:08.348",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "API调用成功，获取到下一页token: , 开始处理订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:32:08.432",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始处理 0 个订单" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:32:08.433",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:32:08.600",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步事务执行完成，下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:32:08.601",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步方法执行完成，返回下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:33:09.244",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-27",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Fri Aug 01 09:33:09 CST 2025 至 Mon Aug 04 09:33:09 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:34:09.842",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-4",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Fri Aug 01 09:33:38 CST 2025, endTime: Mon Aug 04 09:33:38 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:34:10.102",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-4",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:34:10.389",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-4",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "API调用成功，获取到下一页token: , 开始处理订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:34:10.473",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-4",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始处理 0 个订单" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:34:10.473",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-4",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:34:10.641",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-4",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步事务执行完成，下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:34:10.641",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-4",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步方法执行完成，返回下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:35:11.267",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-30",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Fri Aug 01 09:35:11 CST 2025 至 Mon Aug 04 09:35:11 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:36:11.733",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-2",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Fri Aug 01 09:35:39 CST 2025, endTime: Mon Aug 04 09:35:39 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:36:11.999",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-2",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:36:12.298",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-2",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "API调用成功，获取到下一页token: , 开始处理订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:36:12.383",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-2",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始处理 0 个订单" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:36:12.383",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-2",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:36:12.551",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-2",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步事务执行完成，下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:36:12.552",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-2",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步方法执行完成，返回下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:37:13.183",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-18",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Fri Aug 01 09:37:12 CST 2025 至 Mon Aug 04 09:37:12 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:37:47.856",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-19",
                    "class": "c.g.a.task.tiktok.TiktokTokenRefreshTask",
                    "message": "---TiktokTokenRefreshTask task------produce Data with fixed rate task: Execution Time - Mon Aug 04 09:37:47 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:37:47.942",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-19",
                    "class": "c.g.a.task.tiktok.TiktokTokenRefreshTask",
                    "message": "Tiktok access_token 未到刷新时间，当前时间: 1754271467，过期时间: 1781838424" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:13.642",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-20",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Fri Aug 01 09:37:39 CST 2025, endTime: Mon Aug 04 09:37:39 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:13.897",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-20",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:14.193",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-20",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "API调用成功，获取到下一页token: , 开始处理订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:14.278",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-20",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始处理 0 个订单" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:14.278",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-20",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:14.448",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-20",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步事务执行完成，下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:14.449",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-20",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步方法执行完成，返回下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:36.001",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:36.001",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:36.001",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:36.001",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:36.011",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.ValidateCodeController.get()，prams：[]，cost time：8909100 ns，cost：8 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:36.012",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:36.013",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:36.434",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getLoginPic()，prams：[]，cost time：431639500 ns，cost：431 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:36.435",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:36.435",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:41.842",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:41.843",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.303",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.SystemAdminLogin(SystemAdminLoginRequest,HttpServletRequest)，prams：[SystemAdminLoginRequest(account=admin, pwd=BzX36YjvNR8o, key=4459c2380f1fbfc56c881589ad7cccf6, code=tkpx), SecurityContextHolderAwareRequestWrapper[ org.springframework.security.web.header.HeaderWriterFilter$HeaderWriterRequest@5689aa60]]，cost time：********* ns，cost：458 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.304",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.304",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-12",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.318",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.318",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.318",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.318",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.318",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：53500 ns，cost：0 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.320",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.321",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-15",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.331",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.331",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.405",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemStoreStaffController.getList(Integer,PageParamRequest)，prams：[0, PageParamRequest(page=1, limit=9999)]，cost time：85102500 ns，cost：85 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.409",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.409",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-9",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.851",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：518952600 ns，cost：518 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.854",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.854",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.959",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-21",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.959",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-21",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.959",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.960",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.960",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.960",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:43.048",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：88396300 ns，cost：88 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:43.051",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:43.051",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-17",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:43.207",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemMenuController.getList(SystemMenuSearchRequest)，prams：[SystemMenuSearchRequest(name=, menuType=)]，cost time：245847000 ns，cost：245 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:43.210",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:43.211",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-19",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:43.212",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-21",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：252351500 ns，cost：252 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:43.214",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-21",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:43.214",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-21",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.492",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.493",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.494",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：81800 ns，cost：0 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.496",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.497",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-23",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.507",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.507",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.764",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：256115900 ns，cost：256 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.767",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.767",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-24",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.820",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.820",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.821",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.821",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.821",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.821",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.899",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：76932000 ns，cost：76 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.902",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.902",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-5",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.908",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：85330600 ns，cost：85 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.910",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.911",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-4",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:48.589",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemMenuController.getList(SystemMenuSearchRequest)，prams：[SystemMenuSearchRequest(name=, menuType=)]，cost time：766539900 ns，cost：766 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:48.593",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:48.593",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-28",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:15.962",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-11",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Fri Aug 01 09:39:15 CST 2025 至 Mon Aug 04 09:39:15 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:18.758",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:18.758",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:18.758",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:18.758",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:18.935",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemGroupDataController.getList(SystemGroupDataSearchRequest,PageParamRequest)，prams：[SystemGroupDataSearchRequest(keywords=null, gid=74, status=null), PageParamRequest(page=1, limit=20)]，cost time：175003000 ns，cost：175 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:18.939",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:18.939",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:24.364",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.StoreBrandController.getBrandList(Integer,String,PageParamRequest)，prams：[-1, , PageParamRequest(page=1, limit=20)]，cost time：5604300000 ns，cost：5604 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:24.365",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:24.365",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:47.778",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-26",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:47.778",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:47.779",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:47.779",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-26",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:52.833",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-26",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.StoreBrandController.getBrandList(Integer,String,PageParamRequest)，prams：[-1, , PageParamRequest(page=1, limit=10000)]，cost time：5053295400 ns，cost：5053 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:52.838",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-26",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:52.838",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-26",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:40:08.603",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.StoreProductController.getList(StoreProductSearchRequest,PageParamRequest)，prams：[StoreProductSearchRequest(type=0, cateId=null, keywords=, isShow=null, brand=), PageParamRequest(page=1, limit=20)]，cost time：20821700400 ns，cost：20821 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:40:08.609",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:40:08.609",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-3",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:40:17.269",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-23",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Fri Aug 01 09:39:40 CST 2025, endTime: Mon Aug 04 09:39:40 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:40:18.031",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-23",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:40:28.373",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-23",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "API调用成功，获取到下一页token: , 开始处理订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:40:28.634",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-23",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始处理 0 个订单" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:40:28.634",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-23",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:40:29.139",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-23",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步事务执行完成，下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:40:29.139",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-23",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步方法执行完成，返回下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:41:00.791",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:41:00.792",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:41:01.049",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.SystemConfigController.info(Integer)，prams：[100]，cost time：256065600 ns，cost：256 ms" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:41:01.053",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:41:01.054",
                    "level": "INFO",
                    "thread": "http-nio-20000-exec-14",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:41:31.050",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-13",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Fri Aug 01 09:41:30 CST 2025 至 Mon Aug 04 09:41:30 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:42:32.346",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-26",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Fri Aug 01 09:41:40 CST 2025, endTime: Mon Aug 04 09:41:40 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:42:33.102",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-26",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:42:33.369",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-26",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "API调用成功，获取到下一页token: , 开始处理订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:42:33.623",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-26",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始处理 0 个订单" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:42:33.624",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-26",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:42:34.123",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-26",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步事务执行完成，下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:42:34.123",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-26",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步方法执行完成，返回下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:43:35.927",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-14",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Fri Aug 01 09:43:35 CST 2025 至 Mon Aug 04 09:43:35 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:44:37.218",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-29",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Fri Aug 01 09:43:41 CST 2025, endTime: Mon Aug 04 09:43:41 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:44:37.999",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-29",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:44:38.270",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-29",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "API调用成功，获取到下一页token: , 开始处理订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:44:38.527",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-29",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始处理 0 个订单" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:44:38.528",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-29",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:44:39.031",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-29",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步事务执行完成，下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:44:39.031",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-29",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步方法执行完成，返回下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:45:40.852",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-8",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Fri Aug 01 09:45:40 CST 2025, endTime: Mon Aug 04 09:45:40 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:45:41.619",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-8",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:45:41.972",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-8",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "API调用成功，获取到下一页token: , 开始处理订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:45:42.221",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-8",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始处理 0 个订单" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:45:42.221",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-8",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:45:42.734",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-8",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步事务执行完成，下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:45:42.734",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-8",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步方法执行完成，返回下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:46:44.544",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-17",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Fri Aug 01 09:46:41 CST 2025, endTime: Mon Aug 04 09:46:41 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:46:45.311",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-17",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:46:45.712",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-17",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "API调用成功，获取到下一页token: , 开始处理订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:46:45.965",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-17",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始处理 0 个订单" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:46:45.966",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-17",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:46:46.462",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-17",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步事务执行完成，下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:46:46.462",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-17",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步方法执行完成，返回下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:47:47.945",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-10",
                    "class": "c.g.a.task.tiktok.TiktokTokenRefreshTask",
                    "message": "---TiktokTokenRefreshTask task------produce Data with fixed rate task: Execution Time - Mon Aug 04 09:47:47 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:47:48.129",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-10",
                    "class": "c.g.a.task.tiktok.TiktokTokenRefreshTask",
                    "message": "Tiktok access_token 未到刷新时间，当前时间: 1754272067，过期时间: 1781838424" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:47:48.270",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-19",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Fri Aug 01 09:47:47 CST 2025 至 Mon Aug 04 09:47:47 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:48:49.562",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-21",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Fri Aug 01 09:48:43 CST 2025, endTime: Mon Aug 04 09:48:43 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:48:50.337",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-21",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:48:50.586",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-21",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "API调用成功，获取到下一页token: , 开始处理订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:48:50.852",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-21",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始处理 0 个订单" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:48:50.853",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-21",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:48:51.352",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-21",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步事务执行完成，下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:48:51.352",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-21",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步方法执行完成，返回下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:49:53.169",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-6",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Fri Aug 01 09:49:52 CST 2025 至 Mon Aug 04 09:49:52 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:50:54.454",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-24",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Fri Aug 01 09:50:46 CST 2025, endTime: Mon Aug 04 09:50:46 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:50:55.224",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-24",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:50:55.510",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-24",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "API调用成功，获取到下一页token: , 开始处理订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:50:55.763",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-24",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始处理 0 个订单" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:50:55.763",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-24",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:50:56.268",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-24",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步事务执行完成，下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:50:56.269",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-24",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步方法执行完成，返回下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:51:58.081",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-1",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Fri Aug 01 09:51:57 CST 2025 至 Mon Aug 04 09:51:57 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:52:59.381",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-27",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Fri Aug 01 09:52:48 CST 2025, endTime: Mon Aug 04 09:52:48 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:53:00.230",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-27",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:53:00.555",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-27",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "API调用成功，获取到下一页token: , 开始处理订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:53:00.795",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-27",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始处理 0 个订单" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:53:00.795",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-27",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:53:01.311",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-27",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步事务执行完成，下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:53:01.311",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-27",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步方法执行完成，返回下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:54:02.391",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-29",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Fri Aug 01 09:54:02 CST 2025 至 Mon Aug 04 09:54:02 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:55:02.837",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-8",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Fri Aug 01 09:54:50 CST 2025, endTime: Mon Aug 04 09:54:50 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:55:03.103",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-8",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:55:03.429",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-8",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "API调用成功，获取到下一页token: , 开始处理订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:55:03.521",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-8",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始处理 0 个订单" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:55:03.521",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-8",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:55:03.687",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-8",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步事务执行完成，下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:55:03.687",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-8",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步方法执行完成，返回下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:56:04.323",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-9",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Fri Aug 01 09:56:04 CST 2025 至 Mon Aug 04 09:56:04 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:57:04.757",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Fri Aug 01 09:56:52 CST 2025, endTime: Mon Aug 04 09:56:52 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:57:05.028",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:57:05.414",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "API调用成功，获取到下一页token: , 开始处理订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:57:05.502",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始处理 0 个订单" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:57:05.502",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:57:05.660",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步事务执行完成，下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:57:05.660",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-5",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步方法执行完成，返回下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:57:48.135",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-19",
                    "class": "c.g.a.task.tiktok.TiktokTokenRefreshTask",
                    "message": "---TiktokTokenRefreshTask task------produce Data with fixed rate task: Execution Time - Mon Aug 04 09:57:48 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:57:48.219",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-19",
                    "class": "c.g.a.task.tiktok.TiktokTokenRefreshTask",
                    "message": "Tiktok access_token 未到刷新时间，当前时间: 1754272668，过期时间: 1781838424" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:58:06.299",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-3",
                    "class": "c.g.admin.task.order.OrderTiktokPullTask",
                    "message": "创建TikTok订单拉取初始任务，时间范围：Fri Aug 01 09:58:06 CST 2025 至 Mon Aug 04 09:58:06 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:59:06.817",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-22",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始同步TikTok订单 - pageToken: null, startTime: Fri Aug 01 09:58:54 CST 2025, endTime: Mon Aug 04 09:58:54 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:59:07.077",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-22",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始调用TikTok API获取订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:59:07.395",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-22",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "API调用成功，获取到下一页token: , 开始处理订单数据" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:59:07.529",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-22",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "开始处理 0 个订单" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:59:07.529",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-22",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:59:07.697",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-22",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步事务执行完成，下一页token: " }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:59:07.697",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-22",
                    "class": "c.g.s.s.impl.TiktokOrderSyncServiceImpl",
                    "message": "TikTok订单同步方法执行完成，返回下一页token: " }
                    
