{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:16:44.868",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.l.ClasspathLoggingApplicationListener",
                    "message": "Application started with classpath: [file:/D:/Java/jre/lib/charsets.jar, file:/D:/Java/jre/lib/deploy.jar, file:/D:/Java/jre/lib/ext/access-bridge-64.jar, file:/D:/Java/jre/lib/ext/cldrdata.jar, file:/D:/Java/jre/lib/ext/dnsns.jar, file:/D:/Java/jre/lib/ext/jaccess.jar, file:/D:/Java/jre/lib/ext/localedata.jar, file:/D:/Java/jre/lib/ext/nashorn.jar, file:/D:/Java/jre/lib/ext/sunec.jar, file:/D:/Java/jre/lib/ext/sunjce_provider.jar, file:/D:/Java/jre/lib/ext/sunmscapi.jar, file:/D:/Java/jre/lib/ext/sunpkcs11.jar, file:/D:/Java/jre/lib/ext/zipfs.jar, file:/D:/Java/jre/lib/javaws.jar, file:/D:/Java/jre/lib/jce.jar, file:/D:/Java/jre/lib/jfr.jar, file:/D:/Java/jre/lib/jsse.jar, file:/D:/Java/jre/lib/management-agent.jar, file:/D:/Java/jre/lib/plugin.jar, file:/D:/Java/jre/lib/resources.jar, file:/D:/Java/jre/lib/rt.jar, file:/C:/Users/<USER>/Desktop/%e6%96%b0%e5%bb%ba%e6%96%87%e4%bb%b6%e5%a4%b9%20(3)/api/genco-front/target/classes/, file:/C:/Users/<USER>/Desktop/%e6%96%b0%e5%bb%ba%e6%96%87%e4%bb%b6%e5%a4%b9%20(3)/api/genco-service/target/classes/, file:/C:/Users/<USER>/Desktop/%e6%96%b0%e5%bb%ba%e6%96%87%e4%bb%b6%e5%a4%b9%20(3)/api/genco-common/target/classes/, file:/D:/MavenRepository/javax/servlet/javax.servlet-api/4.0.1/javax.servlet-api-4.0.1.jar, file:/D:/MavenRepository/javax/servlet/jstl/1.2/jstl-1.2.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-jasper/9.0.33/tomcat-embed-jasper-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-core/9.0.33/tomcat-embed-core-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-annotations-api/9.0.33/tomcat-annotations-api-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-el/9.0.33/tomcat-embed-el-9.0.33.jar, file:/D:/MavenRepository/org/eclipse/jdt/ecj/3.18.0/ecj-3.18.0.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-jsp-api/9.0.33/tomcat-jsp-api-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-el-api/9.0.33/tomcat-el-api-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-servlet-api/9.0.33/tomcat-servlet-api-9.0.33.jar, file:/D:/MavenRepository/commons-configuration/commons-configuration/1.10/commons-configuration-1.10.jar, file:/D:/MavenRepository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar, file:/D:/MavenRepository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1.jar, file:/D:/MavenRepository/org/apache/velocity/velocity/1.7/velocity-1.7.jar, file:/D:/MavenRepository/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1.jar, file:/D:/MavenRepository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, file:/D:/MavenRepository/com/alibaba/druid/1.1.20/druid-1.1.20.jar, file:/D:/MavenRepository/mysql/mysql-connector-java/8.0.29/mysql-connector-java-8.0.29.jar, file:/D:/MavenRepository/com/google/protobuf/protobuf-java/3.19.4/protobuf-java-3.19.4.jar, file:/D:/MavenRepository/org/projectlombok/lombok/1.18.12/lombok-1.18.12.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-boot-starter/3.3.1/mybatis-plus-boot-starter-3.3.1.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-test/2.2.6.RELEASE/spring-boot-test-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot/2.2.6.RELEASE/spring-boot-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-context/5.2.5.RELEASE/spring-context-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-test/5.2.5.RELEASE/spring-test-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-core/5.2.5.RELEASE/spring-core-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-jcl/5.2.5.RELEASE/spring-jcl-5.2.5.RELEASE.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus/3.3.1/mybatis-plus-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-extension/3.3.1/mybatis-plus-extension-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-core/3.3.1/mybatis-plus-core-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-annotation/3.3.1/mybatis-plus-annotation-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-generator/3.3.1/mybatis-plus-generator-3.3.1.jar, file:/D:/MavenRepository/io/swagger/swagger-annotations/1.5.21/swagger-annotations-1.5.21.jar, file:/D:/MavenRepository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar, file:/D:/MavenRepository/net/bytebuddy/byte-buddy/1.10.8/byte-buddy-1.10.8.jar, file:/D:/MavenRepository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar, file:/D:/MavenRepository/com/google/guava/guava/20.0/guava-20.0.jar, file:/D:/MavenRepository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar, file:/D:/MavenRepository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar, file:/D:/MavenRepository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, file:/D:/MavenRepository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, file:/D:/MavenRepository/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar, file:/D:/MavenRepository/io/swagger/swagger-models/1.5.22/swagger-models-1.5.22.jar, file:/D:/MavenRepository/com/fasterxml/jackson/core/jackson-annotations/2.10.3/jackson-annotations-2.10.3.jar, file:/D:/MavenRepository/com/github/xiaoymin/swagger-bootstrap-ui/1.9.3/swagger-bootstrap-ui-1.9.3.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-autoconfigure/2.2.6.RELEASE/spring-boot-autoconfigure-2.2.6.RELEASE.jar, file:/D:/MavenRepository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/D:/MavenRepository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/D:/MavenRepository/net/logstash/logback/logstash-logback-encoder/5.3/logstash-logback-encoder-5.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/core/jackson-databind/2.10.3/jackson-databind-2.10.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/core/jackson-core/2.10.3/jackson-core-2.10.3.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-web/2.2.6.RELEASE/spring-boot-starter-web-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-json/2.2.6.RELEASE/spring-boot-starter-json-2.2.6.RELEASE.jar, file:/D:/MavenRepository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.3/jackson-datatype-jdk8-2.10.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.3/jackson-datatype-jsr310-2.10.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.3/jackson-module-parameter-names-2.10.3.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-tomcat/2.2.6.RELEASE/spring-boot-starter-tomcat-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.33/tomcat-embed-websocket-9.0.33.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-validation/2.2.6.RELEASE/spring-boot-starter-validation-2.2.6.RELEASE.jar, file:/D:/MavenRepository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar, file:/D:/MavenRepository/org/hibernate/validator/hibernate-validator/6.0.18.Final/hibernate-validator-6.0.18.Final.jar, file:/D:/MavenRepository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar, file:/D:/MavenRepository/org/springframework/spring-web/5.2.5.RELEASE/spring-web-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-webmvc/5.2.5.RELEASE/spring-webmvc-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-expression/5.2.5.RELEASE/spring-expression-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-data-redis/2.2.0.RELEASE/spring-boot-starter-data-redis-2.2.0.RELEASE.jar, file:/D:/MavenRepository/org/springframework/data/spring-data-redis/2.2.6.RELEASE/spring-data-redis-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/data/spring-data-keyvalue/2.2.6.RELEASE/spring-data-keyvalue-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-tx/5.2.5.RELEASE/spring-tx-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-oxm/5.2.5.RELEASE/spring-oxm-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-context-support/5.2.5.RELEASE/spring-context-support-5.2.5.RELEASE.jar, file:/D:/MavenRepository/io/lettuce/lettuce-core/5.2.2.RELEASE/lettuce-core-5.2.2.RELEASE.jar, file:/D:/MavenRepository/io/netty/netty-common/4.1.48.Final/netty-common-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-handler/4.1.48.Final/netty-handler-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-resolver/4.1.48.Final/netty-resolver-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-buffer/4.1.48.Final/netty-buffer-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-codec/4.1.48.Final/netty-codec-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-transport/4.1.48.Final/netty-transport-4.1.48.Final.jar, file:/D:/MavenRepository/io/projectreactor/reactor-core/3.3.4.RELEASE/reactor-core-3.3.4.RELEASE.jar, file:/D:/MavenRepository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar, file:/D:/MavenRepository/redis/clients/jedis/3.1.0/jedis-3.1.0.jar, file:/D:/MavenRepository/org/apache/commons/commons-pool2/2.7.0/commons-pool2-2.7.0.jar, file:/D:/MavenRepository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.5/pagehelper-spring-boot-starter-1.2.5.jar, file:/D:/MavenRepository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.5/pagehelper-spring-boot-autoconfigure-1.2.5.jar, file:/D:/MavenRepository/com/github/pagehelper/pagehelper/5.1.4/pagehelper-5.1.4.jar, file:/D:/MavenRepository/com/github/jsqlparser/jsqlparser/1.0/jsqlparser-1.0.jar, file:/D:/MavenRepository/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final.jar, file:/D:/MavenRepository/org/springframework/data/spring-data-commons/2.2.6.RELEASE/spring-data-commons-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-beans/5.2.5.RELEASE/spring-beans-5.2.5.RELEASE.jar, file:/D:/MavenRepository/cn/hutool/hutool-all/4.5.7/hutool-all-4.5.7.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-actuator/2.2.6.RELEASE/spring-boot-starter-actuator-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.2.6.RELEASE/spring-boot-actuator-autoconfigure-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-actuator/2.2.6.RELEASE/spring-boot-actuator-2.2.6.RELEASE.jar, file:/D:/MavenRepository/io/micrometer/micrometer-core/1.3.6/micrometer-core-1.3.6.jar, file:/D:/MavenRepository/org/hdrhistogram/HdrHistogram/2.1.11/HdrHistogram-2.1.11.jar, file:/D:/MavenRepository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar, file:/D:/MavenRepository/org/apache/httpcomponents/httpclient/4.5.6/httpclient-4.5.6.jar, file:/D:/MavenRepository/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.jar, file:/D:/MavenRepository/commons-codec/commons-codec/1.13/commons-codec-1.13.jar, file:/D:/MavenRepository/org/apache/commons/commons-lang3/3.5/commons-lang3-3.5.jar, file:/D:/MavenRepository/org/apache/poi/poi/3.17/poi-3.17.jar, file:/D:/MavenRepository/org/apache/commons/commons-collections4/4.1/commons-collections4-4.1.jar, file:/D:/MavenRepository/org/apache/poi/poi-ooxml/3.17/poi-ooxml-3.17.jar, file:/D:/MavenRepository/org/apache/poi/poi-ooxml-schemas/3.17/poi-ooxml-schemas-3.17.jar, file:/D:/MavenRepository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar, file:/D:/MavenRepository/com/github/virtuald/curvesapi/1.04/curvesapi-1.04.jar, file:/D:/MavenRepository/commons-fileupload/commons-fileupload/1.3.3/commons-fileupload-1.3.3.jar, file:/D:/MavenRepository/commons-io/commons-io/2.4/commons-io-2.4.jar, file:/D:/MavenRepository/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar, file:/D:/MavenRepository/com/aliyun/oss/aliyun-sdk-oss/3.5.0/aliyun-sdk-oss-3.5.0.jar, file:/D:/MavenRepository/org/jdom/jdom/1.1/jdom-1.1.jar, file:/D:/MavenRepository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar, file:/D:/MavenRepository/stax/stax-api/1.0.1/stax-api-1.0.1.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar, file:/D:/MavenRepository/com/qcloud/cos_api/5.6.22/cos_api-5.6.22.jar, file:/D:/MavenRepository/joda-time/joda-time/2.10.5/joda-time-2.10.5.jar, file:/D:/MavenRepository/org/bouncycastle/bcprov-jdk15on/1.64/bcprov-jdk15on-1.64.jar, file:/D:/MavenRepository/com/qiniu/qiniu-java-sdk/7.2.28/qiniu-java-sdk-7.2.28.jar, file:/D:/MavenRepository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar, file:/D:/MavenRepository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar, file:/D:/MavenRepository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar, file:/D:/MavenRepository/com/thoughtworks/xstream/xstream/1.4.18/xstream-1.4.18.jar, file:/D:/MavenRepository/io/github/x-stream/mxparser/1.2.2/mxparser-1.2.2.jar, file:/D:/MavenRepository/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar, file:/D:/MavenRepository/org/mongodb/mongodb-driver-core/3.8.2/mongodb-driver-core-3.8.2.jar, file:/D:/MavenRepository/org/mongodb/bson/3.11.2/bson-3.11.2.jar, file:/D:/MavenRepository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar, file:/D:/MavenRepository/org/apache/httpcomponents/httpmime/4.5.2/httpmime-4.5.2.jar, file:/D:/MavenRepository/com/google/zxing/core/3.3.3/core-3.3.3.jar, file:/D:/MavenRepository/com/google/zxing/javase/3.3.3/javase-3.3.3.jar, file:/D:/MavenRepository/com/beust/jcommander/1.72/jcommander-1.72.jar, file:/D:/MavenRepository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar, file:/D:/MavenRepository/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar, file:/D:/MavenRepository/io/jsonwebtoken/jjwt/0.9.1/jjwt-0.9.1.jar, file:/D:/MavenRepository/com/auth0/jwks-rsa/0.9.0/jwks-rsa-0.9.0.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-security/2.2.6.RELEASE/spring-boot-starter-security-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/security/spring-security-config/5.2.2.RELEASE/spring-security-config-5.2.2.RELEASE.jar, file:/D:/MavenRepository/org/springframework/security/spring-security-core/5.2.2.RELEASE/spring-security-core-5.2.2.RELEASE.jar, file:/D:/MavenRepository/org/springframework/security/spring-security-web/5.2.2.RELEASE/spring-security-web-5.2.2.RELEASE.jar, file:/D:/MavenRepository/com/tiktokshop/open-sdk-java/1.0.0/open-sdk-java-1.0.0.jar, file:/D:/MavenRepository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar, file:/D:/MavenRepository/io/gsonfire/gson-fire/1.9.0/gson-fire-1.9.0.jar, file:/D:/MavenRepository/org/openapitools/jackson-databind-nullable/0.2.6/jackson-databind-nullable-0.2.6.jar, file:/D:/MavenRepository/javax/ws/rs/jsr311-api/1.1.1/jsr311-api-1.1.1.jar, file:/D:/MavenRepository/javax/ws/rs/javax.ws.rs-api/2.1.1/javax.ws.rs-api-2.1.1.jar, file:/D:/MavenRepository/com/squareup/okhttp3/okhttp/4.11.0/okhttp-4.11.0.jar, file:/D:/MavenRepository/com/squareup/okio/okio/3.2.0/okio-3.2.0.jar, file:/D:/MavenRepository/com/squareup/okio/okio-jvm/3.2.0/okio-jvm-3.2.0.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib/1.3.71/kotlin-stdlib-1.3.71.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib-common/1.3.71/kotlin-stdlib-common-1.3.71.jar, file:/D:/MavenRepository/org/jetbrains/annotations/13.0/annotations-13.0.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib-jdk8/1.3.71/kotlin-stdlib-jdk8-1.3.71.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib-jdk7/1.3.71/kotlin-stdlib-jdk7-1.3.71.jar, file:/D:/MavenRepository/com/squareup/okhttp3/logging-interceptor/4.11.0/logging-interceptor-4.11.0.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-aop/2.2.6.RELEASE/spring-boot-starter-aop-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter/2.2.6.RELEASE/spring-boot-starter-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-logging/2.2.6.RELEASE/spring-boot-starter-logging-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/apache/logging/log4j/log4j-to-slf4j/2.12.1/log4j-to-slf4j-2.12.1.jar, file:/D:/MavenRepository/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar, file:/D:/MavenRepository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar, file:/D:/MavenRepository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar, file:/D:/MavenRepository/org/yaml/snakeyaml/1.25/snakeyaml-1.25.jar, file:/D:/MavenRepository/org/springframework/spring-aop/5.2.5.RELEASE/spring-aop-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/aspectj/aspectjweaver/1.9.5/aspectjweaver-1.9.5.jar, file:/D:/MavenRepository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.2.0/mybatis-spring-boot-starter-2.2.0.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-jdbc/2.2.6.RELEASE/spring-boot-starter-jdbc-2.2.6.RELEASE.jar, file:/D:/MavenRepository/com/zaxxer/HikariCP/3.4.2/HikariCP-3.4.2.jar, file:/D:/MavenRepository/org/springframework/spring-jdbc/5.2.5.RELEASE/spring-jdbc-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.2.0/mybatis-spring-boot-autoconfigure-2.2.0.jar, file:/D:/MavenRepository/org/mybatis/mybatis/3.5.7/mybatis-3.5.7.jar, file:/D:/MavenRepository/org/mybatis/mybatis-spring/2.0.6/mybatis-spring-2.0.6.jar, file:/D:/Idea/IntelliJ%20IDEA%202025.1.4.1/lib/idea_rt.jar]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:16:44.954",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.boot.SpringApplication",
                    "message": "Loading source class com.genco.front.GencoFrontApplication" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:16:44.997",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Activated activeProfiles prod" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:16:44.997",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'file:/C:/Users/<USER>/Desktop/%e6%96%b0%e5%bb%ba%e6%96%87%e4%bb%b6%e5%a4%b9%20(3)/api/genco-front/target/classes/application.yml' (classpath:/application.yml)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:16:44.997",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Profiles already activated, '[prod]' will not be applied" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:16:44.997",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'file:/C:/Users/<USER>/Desktop/%e6%96%b0%e5%bb%ba%e6%96%87%e4%bb%b6%e5%a4%b9%20(3)/api/genco-front/target/classes/application-prod.yml' (classpath:/application-prod.yml) for profile prod" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:16:44.998",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@62452cc9" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:16:46.859",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: D:\MavenRepository\org\springframework\boot\spring-boot\2.2.6.RELEASE\spring-boot-2.2.6.RELEASE.jar" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:16:46.859",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: D:\MavenRepository\org\springframework\boot\spring-boot\2.2.6.RELEASE\spring-boot-2.2.6.RELEASE.jar" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:16:46.859",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored." }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:16:47.085",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.web.context.ContextLoader",
                    "message": "Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:16:47.439",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:16:47.439",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping servlets: statViewServlet urls=[/druid/*], dispatcherServlet urls=[/]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:16:47.454",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.m.w.servlet.WebMvcMetricsFilter",
                    "message": "Filter 'webMvcMetricsFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:16:47.454",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedRequestContextFilter",
                    "message": "Filter 'requestContextFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:16:47.454",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.web.filter.CorsFilter",
                    "message": "Filter 'corsFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:16:47.454",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedCharacterEncodingFilter",
                    "message": "Filter 'characterEncodingFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:16:47.454",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.DelegatingFilterProxyRegistrationBean$1",
                    "message": "Filter 'springSecurityFilterChain' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:16:47.455",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedFormContentFilter",
                    "message": "Filter 'formContentFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:16:50.238",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "160 mappings in 'requestMappingHandlerMapping'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:16:50.678",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin",
                    "message": "Application Admin MBean registered with name 'org.springframework.boot:type=Admin,name=SpringApplication'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:16:50.686",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerAdapter",
                    "message": "ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:16:50.718",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.handler.SimpleUrlHandlerMapping",
                    "message": "Patterns [/webjars/**, /**, /doc.html] in 'resourceHandlerMapping'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:16:50.726",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:16:51.896",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(1)-************",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Detected StandardServletMultipartResolver" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:16:51.908",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(1)-************",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:16:53.934",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(3)-************",
                    "class": "o.springframework.jdbc.core.JdbcTemplate",
                    "message": "Executing SQL query [/* ping */ SELECT 1]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:22.152",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@62452cc9, started on Mon Aug 04 09:16:44 CST 2025" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:22.156",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Beans' from the JMX domain" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:22.157",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Caches' from the JMX domain" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:22.157",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Health' from the JMX domain" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:22.157",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Info' from the JMX domain" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:22.157",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Conditions' from the JMX domain" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:22.158",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Configprops' from the JMX domain" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:22.158",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Env' from the JMX domain" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:22.158",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Loggers' from the JMX domain" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:22.158",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Threaddump' from the JMX domain" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:22.158",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Metrics' from the JMX domain" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:22.158",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Scheduledtasks' from the JMX domain" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:22.158",
                    "level": "DEBUG",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.b.a.endpoint.jmx.JmxEndpointExporter",
                    "message": "Unregister endpoint with ObjectName 'org.springframework.boot:type=Endpoint,name=Mappings' from the JMX domain" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:32.303",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.l.ClasspathLoggingApplicationListener",
                    "message": "Application started with classpath: [file:/D:/Java/jre/lib/charsets.jar, file:/D:/Java/jre/lib/deploy.jar, file:/D:/Java/jre/lib/ext/access-bridge-64.jar, file:/D:/Java/jre/lib/ext/cldrdata.jar, file:/D:/Java/jre/lib/ext/dnsns.jar, file:/D:/Java/jre/lib/ext/jaccess.jar, file:/D:/Java/jre/lib/ext/localedata.jar, file:/D:/Java/jre/lib/ext/nashorn.jar, file:/D:/Java/jre/lib/ext/sunec.jar, file:/D:/Java/jre/lib/ext/sunjce_provider.jar, file:/D:/Java/jre/lib/ext/sunmscapi.jar, file:/D:/Java/jre/lib/ext/sunpkcs11.jar, file:/D:/Java/jre/lib/ext/zipfs.jar, file:/D:/Java/jre/lib/javaws.jar, file:/D:/Java/jre/lib/jce.jar, file:/D:/Java/jre/lib/jfr.jar, file:/D:/Java/jre/lib/jsse.jar, file:/D:/Java/jre/lib/management-agent.jar, file:/D:/Java/jre/lib/plugin.jar, file:/D:/Java/jre/lib/resources.jar, file:/D:/Java/jre/lib/rt.jar, file:/C:/Users/<USER>/Desktop/%e6%96%b0%e5%bb%ba%e6%96%87%e4%bb%b6%e5%a4%b9%20(3)/api/genco-admin/target/classes/, file:/C:/Users/<USER>/Desktop/%e6%96%b0%e5%bb%ba%e6%96%87%e4%bb%b6%e5%a4%b9%20(3)/api/genco-service/target/classes/, file:/C:/Users/<USER>/Desktop/%e6%96%b0%e5%bb%ba%e6%96%87%e4%bb%b6%e5%a4%b9%20(3)/api/genco-common/target/classes/, file:/D:/MavenRepository/javax/servlet/javax.servlet-api/4.0.1/javax.servlet-api-4.0.1.jar, file:/D:/MavenRepository/javax/servlet/jstl/1.2/jstl-1.2.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-jasper/9.0.33/tomcat-embed-jasper-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-core/9.0.33/tomcat-embed-core-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-annotations-api/9.0.33/tomcat-annotations-api-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-el/9.0.33/tomcat-embed-el-9.0.33.jar, file:/D:/MavenRepository/org/eclipse/jdt/ecj/3.18.0/ecj-3.18.0.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-jsp-api/9.0.33/tomcat-jsp-api-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-el-api/9.0.33/tomcat-el-api-9.0.33.jar, file:/D:/MavenRepository/org/apache/tomcat/tomcat-servlet-api/9.0.33/tomcat-servlet-api-9.0.33.jar, file:/D:/MavenRepository/commons-configuration/commons-configuration/1.10/commons-configuration-1.10.jar, file:/D:/MavenRepository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar, file:/D:/MavenRepository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1.jar, file:/D:/MavenRepository/org/apache/velocity/velocity/1.7/velocity-1.7.jar, file:/D:/MavenRepository/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1.jar, file:/D:/MavenRepository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, file:/D:/MavenRepository/com/alibaba/druid/1.1.20/druid-1.1.20.jar, file:/D:/MavenRepository/mysql/mysql-connector-java/8.0.29/mysql-connector-java-8.0.29.jar, file:/D:/MavenRepository/com/google/protobuf/protobuf-java/3.19.4/protobuf-java-3.19.4.jar, file:/D:/MavenRepository/org/projectlombok/lombok/1.18.12/lombok-1.18.12.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-boot-starter/3.3.1/mybatis-plus-boot-starter-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus/3.3.1/mybatis-plus-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-extension/3.3.1/mybatis-plus-extension-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-core/3.3.1/mybatis-plus-core-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-annotation/3.3.1/mybatis-plus-annotation-3.3.1.jar, file:/D:/MavenRepository/com/baomidou/mybatis-plus-generator/3.3.1/mybatis-plus-generator-3.3.1.jar, file:/D:/MavenRepository/io/swagger/swagger-annotations/1.5.21/swagger-annotations-1.5.21.jar, file:/D:/MavenRepository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar, file:/D:/MavenRepository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar, file:/D:/MavenRepository/com/google/guava/guava/20.0/guava-20.0.jar, file:/D:/MavenRepository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar, file:/D:/MavenRepository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, file:/D:/MavenRepository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, file:/D:/MavenRepository/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar, file:/D:/MavenRepository/io/swagger/swagger-models/1.5.22/swagger-models-1.5.22.jar, file:/D:/MavenRepository/com/fasterxml/jackson/core/jackson-annotations/2.10.3/jackson-annotations-2.10.3.jar, file:/D:/MavenRepository/com/github/xiaoymin/swagger-bootstrap-ui/1.9.3/swagger-bootstrap-ui-1.9.3.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-autoconfigure/2.2.6.RELEASE/spring-boot-autoconfigure-2.2.6.RELEASE.jar, file:/D:/MavenRepository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/D:/MavenRepository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/D:/MavenRepository/net/logstash/logback/logstash-logback-encoder/5.3/logstash-logback-encoder-5.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/core/jackson-databind/2.10.3/jackson-databind-2.10.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/core/jackson-core/2.10.3/jackson-core-2.10.3.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-web/2.2.6.RELEASE/spring-boot-starter-web-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-json/2.2.6.RELEASE/spring-boot-starter-json-2.2.6.RELEASE.jar, file:/D:/MavenRepository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.3/jackson-datatype-jdk8-2.10.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.3/jackson-datatype-jsr310-2.10.3.jar, file:/D:/MavenRepository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.3/jackson-module-parameter-names-2.10.3.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-tomcat/2.2.6.RELEASE/spring-boot-starter-tomcat-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.33/tomcat-embed-websocket-9.0.33.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-validation/2.2.6.RELEASE/spring-boot-starter-validation-2.2.6.RELEASE.jar, file:/D:/MavenRepository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar, file:/D:/MavenRepository/org/hibernate/validator/hibernate-validator/6.0.18.Final/hibernate-validator-6.0.18.Final.jar, file:/D:/MavenRepository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar, file:/D:/MavenRepository/org/springframework/spring-web/5.2.5.RELEASE/spring-web-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-webmvc/5.2.5.RELEASE/spring-webmvc-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-expression/5.2.5.RELEASE/spring-expression-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-data-redis/2.2.0.RELEASE/spring-boot-starter-data-redis-2.2.0.RELEASE.jar, file:/D:/MavenRepository/org/springframework/data/spring-data-redis/2.2.6.RELEASE/spring-data-redis-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/data/spring-data-keyvalue/2.2.6.RELEASE/spring-data-keyvalue-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-tx/5.2.5.RELEASE/spring-tx-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-oxm/5.2.5.RELEASE/spring-oxm-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-context-support/5.2.5.RELEASE/spring-context-support-5.2.5.RELEASE.jar, file:/D:/MavenRepository/io/lettuce/lettuce-core/5.2.2.RELEASE/lettuce-core-5.2.2.RELEASE.jar, file:/D:/MavenRepository/io/netty/netty-common/4.1.48.Final/netty-common-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-handler/4.1.48.Final/netty-handler-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-resolver/4.1.48.Final/netty-resolver-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-buffer/4.1.48.Final/netty-buffer-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-codec/4.1.48.Final/netty-codec-4.1.48.Final.jar, file:/D:/MavenRepository/io/netty/netty-transport/4.1.48.Final/netty-transport-4.1.48.Final.jar, file:/D:/MavenRepository/io/projectreactor/reactor-core/3.3.4.RELEASE/reactor-core-3.3.4.RELEASE.jar, file:/D:/MavenRepository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar, file:/D:/MavenRepository/redis/clients/jedis/3.1.0/jedis-3.1.0.jar, file:/D:/MavenRepository/org/apache/commons/commons-pool2/2.7.0/commons-pool2-2.7.0.jar, file:/D:/MavenRepository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.5/pagehelper-spring-boot-starter-1.2.5.jar, file:/D:/MavenRepository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.5/pagehelper-spring-boot-autoconfigure-1.2.5.jar, file:/D:/MavenRepository/com/github/pagehelper/pagehelper/5.1.4/pagehelper-5.1.4.jar, file:/D:/MavenRepository/com/github/jsqlparser/jsqlparser/1.0/jsqlparser-1.0.jar, file:/D:/MavenRepository/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final.jar, file:/D:/MavenRepository/org/springframework/data/spring-data-commons/2.2.6.RELEASE/spring-data-commons-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-beans/5.2.5.RELEASE/spring-beans-5.2.5.RELEASE.jar, file:/D:/MavenRepository/cn/hutool/hutool-all/4.5.7/hutool-all-4.5.7.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-actuator/2.2.6.RELEASE/spring-boot-starter-actuator-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.2.6.RELEASE/spring-boot-actuator-autoconfigure-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-actuator/2.2.6.RELEASE/spring-boot-actuator-2.2.6.RELEASE.jar, file:/D:/MavenRepository/io/micrometer/micrometer-core/1.3.6/micrometer-core-1.3.6.jar, file:/D:/MavenRepository/org/hdrhistogram/HdrHistogram/2.1.11/HdrHistogram-2.1.11.jar, file:/D:/MavenRepository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar, file:/D:/MavenRepository/org/apache/httpcomponents/httpclient/4.5.6/httpclient-4.5.6.jar, file:/D:/MavenRepository/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.jar, file:/D:/MavenRepository/commons-codec/commons-codec/1.13/commons-codec-1.13.jar, file:/D:/MavenRepository/org/apache/commons/commons-lang3/3.5/commons-lang3-3.5.jar, file:/D:/MavenRepository/org/apache/poi/poi/3.17/poi-3.17.jar, file:/D:/MavenRepository/org/apache/commons/commons-collections4/4.1/commons-collections4-4.1.jar, file:/D:/MavenRepository/org/apache/poi/poi-ooxml/3.17/poi-ooxml-3.17.jar, file:/D:/MavenRepository/org/apache/poi/poi-ooxml-schemas/3.17/poi-ooxml-schemas-3.17.jar, file:/D:/MavenRepository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar, file:/D:/MavenRepository/com/github/virtuald/curvesapi/1.04/curvesapi-1.04.jar, file:/D:/MavenRepository/commons-fileupload/commons-fileupload/1.3.3/commons-fileupload-1.3.3.jar, file:/D:/MavenRepository/commons-io/commons-io/2.4/commons-io-2.4.jar, file:/D:/MavenRepository/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar, file:/D:/MavenRepository/com/aliyun/oss/aliyun-sdk-oss/3.5.0/aliyun-sdk-oss-3.5.0.jar, file:/D:/MavenRepository/org/jdom/jdom/1.1/jdom-1.1.jar, file:/D:/MavenRepository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar, file:/D:/MavenRepository/stax/stax-api/1.0.1/stax-api-1.0.1.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar, file:/D:/MavenRepository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar, file:/D:/MavenRepository/com/qcloud/cos_api/5.6.22/cos_api-5.6.22.jar, file:/D:/MavenRepository/joda-time/joda-time/2.10.5/joda-time-2.10.5.jar, file:/D:/MavenRepository/org/bouncycastle/bcprov-jdk15on/1.64/bcprov-jdk15on-1.64.jar, file:/D:/MavenRepository/com/qiniu/qiniu-java-sdk/7.2.28/qiniu-java-sdk-7.2.28.jar, file:/D:/MavenRepository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar, file:/D:/MavenRepository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar, file:/D:/MavenRepository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar, file:/D:/MavenRepository/com/thoughtworks/xstream/xstream/1.4.18/xstream-1.4.18.jar, file:/D:/MavenRepository/io/github/x-stream/mxparser/1.2.2/mxparser-1.2.2.jar, file:/D:/MavenRepository/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar, file:/D:/MavenRepository/org/mongodb/mongodb-driver-core/3.8.2/mongodb-driver-core-3.8.2.jar, file:/D:/MavenRepository/org/mongodb/bson/3.11.2/bson-3.11.2.jar, file:/D:/MavenRepository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar, file:/D:/MavenRepository/org/apache/httpcomponents/httpmime/4.5.2/httpmime-4.5.2.jar, file:/D:/MavenRepository/com/google/zxing/core/3.3.3/core-3.3.3.jar, file:/D:/MavenRepository/com/google/zxing/javase/3.3.3/javase-3.3.3.jar, file:/D:/MavenRepository/com/beust/jcommander/1.72/jcommander-1.72.jar, file:/D:/MavenRepository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar, file:/D:/MavenRepository/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar, file:/D:/MavenRepository/io/jsonwebtoken/jjwt/0.9.1/jjwt-0.9.1.jar, file:/D:/MavenRepository/com/auth0/jwks-rsa/0.9.0/jwks-rsa-0.9.0.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-security/2.2.6.RELEASE/spring-boot-starter-security-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/security/spring-security-config/5.2.2.RELEASE/spring-security-config-5.2.2.RELEASE.jar, file:/D:/MavenRepository/org/springframework/security/spring-security-core/5.2.2.RELEASE/spring-security-core-5.2.2.RELEASE.jar, file:/D:/MavenRepository/org/springframework/security/spring-security-web/5.2.2.RELEASE/spring-security-web-5.2.2.RELEASE.jar, file:/D:/MavenRepository/com/tiktokshop/open-sdk-java/1.0.0/open-sdk-java-1.0.0.jar, file:/D:/MavenRepository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar, file:/D:/MavenRepository/io/gsonfire/gson-fire/1.9.0/gson-fire-1.9.0.jar, file:/D:/MavenRepository/org/openapitools/jackson-databind-nullable/0.2.6/jackson-databind-nullable-0.2.6.jar, file:/D:/MavenRepository/javax/ws/rs/jsr311-api/1.1.1/jsr311-api-1.1.1.jar, file:/D:/MavenRepository/javax/ws/rs/javax.ws.rs-api/2.1.1/javax.ws.rs-api-2.1.1.jar, file:/D:/MavenRepository/com/squareup/okhttp3/okhttp/4.11.0/okhttp-4.11.0.jar, file:/D:/MavenRepository/com/squareup/okio/okio/3.2.0/okio-3.2.0.jar, file:/D:/MavenRepository/com/squareup/okio/okio-jvm/3.2.0/okio-jvm-3.2.0.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib/1.3.71/kotlin-stdlib-1.3.71.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib-common/1.3.71/kotlin-stdlib-common-1.3.71.jar, file:/D:/MavenRepository/org/jetbrains/annotations/13.0/annotations-13.0.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib-jdk8/1.3.71/kotlin-stdlib-jdk8-1.3.71.jar, file:/D:/MavenRepository/org/jetbrains/kotlin/kotlin-stdlib-jdk7/1.3.71/kotlin-stdlib-jdk7-1.3.71.jar, file:/D:/MavenRepository/com/squareup/okhttp3/logging-interceptor/4.11.0/logging-interceptor-4.11.0.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-aop/2.2.6.RELEASE/spring-boot-starter-aop-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-aop/5.2.5.RELEASE/spring-aop-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/aspectj/aspectjweaver/1.9.5/aspectjweaver-1.9.5.jar, file:/D:/MavenRepository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.2.0/mybatis-spring-boot-starter-2.2.0.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-jdbc/2.2.6.RELEASE/spring-boot-starter-jdbc-2.2.6.RELEASE.jar, file:/D:/MavenRepository/com/zaxxer/HikariCP/3.4.2/HikariCP-3.4.2.jar, file:/D:/MavenRepository/org/springframework/spring-jdbc/5.2.5.RELEASE/spring-jdbc-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.2.0/mybatis-spring-boot-autoconfigure-2.2.0.jar, file:/D:/MavenRepository/org/mybatis/mybatis/3.5.7/mybatis-3.5.7.jar, file:/D:/MavenRepository/org/mybatis/mybatis-spring/2.0.6/mybatis-spring-2.0.6.jar, file:/D:/MavenRepository/net/minidev/json-smart/2.3/json-smart-2.3.jar, file:/D:/MavenRepository/net/minidev/accessors-smart/1.2/accessors-smart-1.2.jar, file:/D:/MavenRepository/org/ow2/asm/asm/5.0.4/asm-5.0.4.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter/2.2.6.RELEASE/spring-boot-starter-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot/2.2.6.RELEASE/spring-boot-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-context/5.2.5.RELEASE/spring-context-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-starter-logging/2.2.6.RELEASE/spring-boot-starter-logging-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/apache/logging/log4j/log4j-to-slf4j/2.12.1/log4j-to-slf4j-2.12.1.jar, file:/D:/MavenRepository/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar, file:/D:/MavenRepository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar, file:/D:/MavenRepository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar, file:/D:/MavenRepository/org/yaml/snakeyaml/1.25/snakeyaml-1.25.jar, file:/D:/MavenRepository/org/springframework/boot/spring-boot-test/2.2.6.RELEASE/spring-boot-test-2.2.6.RELEASE.jar, file:/D:/MavenRepository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar, file:/D:/MavenRepository/org/springframework/spring-core/5.2.5.RELEASE/spring-core-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-jcl/5.2.5.RELEASE/spring-jcl-5.2.5.RELEASE.jar, file:/D:/MavenRepository/org/springframework/spring-test/5.2.5.RELEASE/spring-test-5.2.5.RELEASE.jar, file:/D:/MavenRepository/net/bytebuddy/byte-buddy/1.10.8/byte-buddy-1.10.8.jar, file:/D:/Idea/IntelliJ%20IDEA%202025.1.4.1/lib/idea_rt.jar, file:/C:/Users/<USER>/AppData/Local/JetBrains/IntelliJIdea2025.1/captureAgent/debugger-agent.jar]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:32.399",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.boot.SpringApplication",
                    "message": "Loading source class com.genco.admin.GencoAdminApplication" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:32.451",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Activated activeProfiles prod" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:32.451",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'file:/C:/Users/<USER>/Desktop/%e6%96%b0%e5%bb%ba%e6%96%87%e4%bb%b6%e5%a4%b9%20(3)/api/genco-admin/target/classes/application.yml' (classpath:/application.yml)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:32.451",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Profiles already activated, '[prod]' will not be applied" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:32.451",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'file:/C:/Users/<USER>/Desktop/%e6%96%b0%e5%bb%ba%e6%96%87%e4%bb%b6%e5%a4%b9%20(3)/api/genco-admin/target/classes/application-prod.yml' (classpath:/application-prod.yml) for profile prod" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:32.452",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@2e029d61" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:35.028",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: D:\MavenRepository\org\springframework\boot\spring-boot\2.2.6.RELEASE\spring-boot-2.2.6.RELEASE.jar" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:35.029",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: D:\MavenRepository\org\springframework\boot\spring-boot\2.2.6.RELEASE\spring-boot-2.2.6.RELEASE.jar" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:35.029",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored." }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:35.348",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.web.context.ContextLoader",
                    "message": "Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:36.111",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/api/admin/*, /api/front/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647, jwtAuthenticationTokenFilter urls=[/*] order=2147483647" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:36.112",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping servlets: statViewServlet urls=[/druid/*], dispatcherServlet urls=[/]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:36.143",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.m.w.servlet.WebMvcMetricsFilter",
                    "message": "Filter 'webMvcMetricsFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:36.143",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedRequestContextFilter",
                    "message": "Filter 'requestContextFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:36.143",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.web.filter.CorsFilter",
                    "message": "Filter 'corsFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:36.143",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedCharacterEncodingFilter",
                    "message": "Filter 'characterEncodingFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:36.144",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.DelegatingFilterProxyRegistrationBean$1",
                    "message": "Filter 'springSecurityFilterChain' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:36.144",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedFormContentFilter",
                    "message": "Filter 'formContentFilter' configured for use" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:42.130",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "295 mappings in 'requestMappingHandlerMapping'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:42.996",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin",
                    "message": "Application Admin MBean registered with name 'org.springframework.boot:type=Admin,name=SpringApplication'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:43.014",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerAdapter",
                    "message": "ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:43.110",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.handler.SimpleUrlHandlerMapping",
                    "message": "Patterns [/webjars/**, /**, /doc.html, /crmebimage/**] in 'resourceHandlerMapping'" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:43.131",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:46.235",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(7)-************",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Detected StandardServletMultipartResolver" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:46.254",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(7)-************",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:47.597",
                    "level": "DEBUG",
                    "thread": "RMI TCP Connection(8)-************",
                    "class": "o.springframework.jdbc.core.JdbcTemplate",
                    "message": "Executing SQL query [/* ping */ SELECT 1]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:58.378",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:58.378",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:58.378",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getLoginPic?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:58.380",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getLoginPic?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:58.437",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getLoginPic()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:58.437",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:58.437",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:58.437",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getLoginPic()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:59.419",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:59.419",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:59.420",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@17a4782d]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:59.420",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5b30d297]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:59.446",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:59.446",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:59.620",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:59.620",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:59.620",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@1ad20ba]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:59.620",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2c437ed3]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:59.621",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:17:59.622",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:07.091",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/login", parameters={}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:07.091",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#SystemAdminLogin(SystemAdminLoginRequest, HttpServletRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:07.262",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [SystemAdminLoginRequest(account=admin, pwd=BzX36YjvNR8o, key=aa315f6647a490ff2df6059d5431ff20, code= (truncated)...]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.104",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.111",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@c488640]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.113",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.187",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/store/staff/list?page=1&limit=9999&temp=1754270289", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.193",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=116e583c7806499a84801862ec8ca825&temp=1754270289", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.193",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemStoreStaffController#getList(Integer, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.193",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.262",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.262",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3940274b]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.270",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.270",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754270289", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.278",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.381",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.381",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2c0ee83f]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.388",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.395",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.395",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@a2adeb2]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.400",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.485",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/info?formId=100&temp=1754270289", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.485",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754270289", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.490",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#info(Integer)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.490",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.490",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754270289", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.492",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.582",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.582",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@70adb5d8]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.589",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.589",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.589",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@25a7c43a]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.595",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.754",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.754",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6a8a394e]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:09.754",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:16.287",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754270296", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:16.292",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754270296", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:16.292",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:16.292",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:16.829",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:16.829",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7fc7db07]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:16.837",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:18.178",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:18.178",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@60278dab]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:18.190",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:29.848",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/menu/list?menuType=&name=&temp=1754270309", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:29.849",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemMenuController#getList(SystemMenuSearchRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:30.087",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:30.087",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@57e424cb]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:18:30.095",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:21:22.544",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/menu/info/429?temp=1754270482", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:21:22.548",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemMenuController#info(Integer)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:21:22.656",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:21:22.656",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@369b207c]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:21:22.661",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:22:36.100",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/store/product/list?keywords=&page=1&limit=20&type=1&isShow=&temp=1754270556", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:22:36.101",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreProductController#getList(StoreProductSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:22:44.405",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:22:44.405",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@43ff5f23]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:22:44.463",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:12.190",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/info?formId=100&temp=1754270592", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:12.190",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#info(Integer)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:12.272",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:12.272",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@79b93c9d]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:12.274",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:18.023",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754270598", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:18.023",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754270598", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:18.024",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:18.024",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:18.205",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:18.205",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@65aff8dd]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:18.205",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:23.922",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:23.922",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2b6c5698]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:23.931",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:26.854",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/store/product/list?page=1&limit=20&keywords=&total=0&isIndex=false&brand=&temp=1754270606", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:26.854",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=10000&type=-1&name=&temp=1754270606", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:26.854",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:26.854",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreProductController#getList(StoreProductSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:31.263",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:31.271",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7d80fb69]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:31.275",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:47.847",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:47.847",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@28f8c1e]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:47.847",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:54.559",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/store/product/list?page=2&limit=20&keywords=&total=0&isIndex=false&brand=&temp=1754270634", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:23:54.560",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreProductController#getList(StoreProductSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:24:21.350",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:24:21.350",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@41ee4aff]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:24:21.350",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:24:35.114",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/store/product/list?keywords=&page=1&limit=20&type=1&isShow=&temp=1754270675", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:24:35.115",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreProductController#getList(StoreProductSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:01.666",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:01.667",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2a469b0]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:01.670",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:27.494",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/store/productShareRecord/list?keyword=&brandCode=&page=1&limit=20&total=0&temp=1754270727", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:27.494",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=999999&name=&type=-1&temp=1754270727", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:27.499",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:27.499",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreProductShareRecordController#getList(StoreProductShareRecordSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:27.675",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:27.675",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@39616a5]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:27.685",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:35.354",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:35.354",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@16f2e13b]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:35.356",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:39.235",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/finance/apply/extract/bank?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:39.235",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/finance/apply/list?tableFromType=&keywords=&dateLimit=&bankName=&walletCode=&extractType=wallet&status=0&page=1&limit=20&total=0&temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:39.236",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.UserExtractController#getExtractBank()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:39.236",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.UserExtractController#getList(UserExtractSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:39.341",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:39.341",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5e7bce28]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:39.341",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:39.500",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:39.500",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5ca552e4]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:39.500",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:41.729",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/finance/apply/list?keywords=&dateLimit=&bankName=&walletCode=&extractType=wallet&status=&page=1&limit=20&total=0&temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:41.729",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/finance/apply/extract/bank?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:41.729",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.UserExtractController#getList(UserExtractSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:41.730",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.UserExtractController#getExtractBank()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:41.991",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:41.991",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@286a1ac4]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:41.991",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:42.016",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:42.016",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2409c97b]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:42.016",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:52.974",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/store/order/list?orderNo=&productTitle=&type=2&dateLimit=&page=1&limit=20&total=0&temp=1754270752", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:52.975",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreOrderController#getList(StoreOrderSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:53.533",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:53.533",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3f6f0388]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:25:53.541",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.552",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=116e583c7806499a84801862ec8ca825&temp=1754270770", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.553",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.555",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.556",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4e0bbb06]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.558",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.617",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754270770", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.618",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.702",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.702",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3c53ac92]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.705",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.786",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754270770", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.786",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754270770", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.786",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/store/order/list?orderNo=&productTitle=&type=2&dateLimit=&page=1&limit=20&total=0&temp=1754270770", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.787",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.787",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.787",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreOrderController#getList(StoreOrderSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.866",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.866",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@91dc920]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:10.866",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:11.033",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:11.033",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@17349d2a]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:11.049",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:11.317",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:11.317",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@30386b90]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:11.322",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:21.077",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/user/level/list?temp=1754270781", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:21.078",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemUserLevelController#getList()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:21.183",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:21.183",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@435f690]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:21.191",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:33.698",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/user/level/list?temp=1754270793", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:33.700",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemUserLevelController#getList()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:33.784",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:33.784",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@544aa70b]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:33.790",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:33.975",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/user/list?keywords=&level=&page=1&limit=20&total=0&temp=1754270793", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:33.975",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.UserController#getList(UserSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:34.423",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:34.423",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5eabfee6]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:34.432",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:53.686",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/user/topUpLog/list?uid=&keyword=&rechargeType=&dateLimit=&payChannel=&walletCode=&bankName=&page=1&limit=20&total=0&temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:53.687",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/finance/apply/extract/bank?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:53.687",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.UserRechargeController#getList(UserRechargeSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:53.687",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.UserExtractController#getExtractBank()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:53.774",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:53.774",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@1e61b8e2]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:53.774",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:54.490",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:54.490",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@78f2b61f]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:26:54.495",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:05.599",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/finance/apply/extract/bank?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:05.599",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/finance/apply/list?keywords=&dateLimit=&bankName=&walletCode=&extractType=wallet&page=1&limit=20&total=0&temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:05.600",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.UserExtractController#getExtractBank()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:05.600",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.UserExtractController#getList(UserExtractSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:05.855",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:05.855",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@1bcad4cf]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:05.859",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:05.860",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:05.860",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@31d6dab8]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:05.861",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:24.061",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/finance/apply/list?keywords=&dateLimit=&bankName=&walletCode=&extractType=wallet&status=&page=1&limit=20&total=0&temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:24.062",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.UserExtractController#getList(UserExtractSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:24.062",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/finance/apply/extract/bank?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:24.062",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.UserExtractController#getExtractBank()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:24.149",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:24.149",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6323fe5f]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:24.149",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:24.831",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:24.831",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@74c5718]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:24.831",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:32.031",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/info?formId=111&temp=1754270852", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:32.031",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#info(Integer)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:32.286",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:32.286",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6c9d0c6d]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:32.294",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:32.302",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/info?formId=112&temp=1754270852", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:32.302",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#info(Integer)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:32.569",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:32.569",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4bddade7]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:32.569",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:39.516",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/info?formId=113&temp=1754270859", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:39.516",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#info(Integer)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:39.773",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:39.773",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5de06023]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:39.773",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:39.789",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/info?formId=114&temp=1754270859", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:39.789",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#info(Integer)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:40.040",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:40.040",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5ceeab49]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:40.048",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:40.060",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/info?formId=115&temp=1754270860", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:40.060",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#info(Integer)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:40.315",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:40.315",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2e26d497]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:40.315",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.074",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=116e583c7806499a84801862ec8ca825&temp=1754270871", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.075",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.079",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.079",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@14c06832]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.081",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.091",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754270871", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.091",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.352",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.352",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@1484378d]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.352",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.441",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/info?formId=113&temp=1754270871", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.441",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754270871", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.441",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754270871", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.441",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.441",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#info(Integer)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.441",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.527",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.527",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@104514a5]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.527",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.604",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.604",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@1719cb43]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.604",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.694",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.694",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@68f46d4b]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:51.694",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:54.984",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/info?formId=113&temp=1754270874", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:54.984",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#info(Integer)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:55.240",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:55.240",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@44dc52a2]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:27:55.240",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:08.207",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/info?formId=103&temp=1754270888", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:08.207",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#info(Integer)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:08.458",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:08.458",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5c6cf0d0]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:08.458",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:14.845",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/info?formId=104&temp=1754270894", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:14.846",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#info(Integer)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:15.101",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:15.101",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3c737b58]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:15.101",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:38.098",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/info?formId=144&temp=1754270918", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:38.098",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#info(Integer)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:38.352",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:38.360",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@61754268]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:28:38.361",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:07.540",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/role/list?page=1&limit=20&temp=1754270947", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:07.541",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemRoleController#getList(SystemRoleSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:08.076",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:08.076",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7fcf6207]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:08.081",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:28.441",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/admin/list?page=1&limit=20&temp=1754270968", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:28.442",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/role/list?page=1&limit=100&temp=1754270968", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:28.442",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemAdminController#getList(SystemAdminRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:28.442",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemRoleController#getList(SystemRoleSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:28.783",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:28.783",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7ca1aa19]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:28.785",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:28.952",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:28.952",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@1537d0e3]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:28.955",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:41.755",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/menu/list?menuType=&name=&temp=1754270981", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:41.756",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemMenuController#getList(SystemMenuSearchRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:42.779",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:42.779",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6b2395f]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:42.783",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.034",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=116e583c7806499a84801862ec8ca825&temp=1754270994", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.035",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.037",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.037",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@7f9ca3dc]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.040",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.063",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754270994", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.064",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.324",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.326",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6400ac8c]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.327",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.402",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/menu/list?menuType=&name=&temp=1754270994", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.402",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754270994", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.402",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754270994", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.402",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.402",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.402",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemMenuController#getList(SystemMenuSearchRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.483",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.483",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5c17bcfc]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.485",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.659",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.659",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6e04760]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.661",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.675",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.675",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5fc8b555]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:29:54.715",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:35.998",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getLoginPic?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:35.998",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/validate/code/get?temp=**********", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:36.000",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getLoginPic()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:36.000",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.ValidateCodeController#get()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:36.012",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:36.012",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3f08bff3]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:36.013",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:36.434",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:36.434",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@644b03fd]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:36.435",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:41.842",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/admin/login", parameters={}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:41.842",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#SystemAdminLogin(SystemAdminLoginRequest, HttpServletRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:41.843",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [SystemAdminLoginRequest(account=admin, pwd=BzX36YjvNR8o, key=4459c2380f1fbfc56c881589ad7cccf6, code= (truncated)...]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.304",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.304",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@46e309cc]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.305",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.316",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/store/staff/list?page=1&limit=9999&temp=1754271522", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.316",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=eda45aa88ce8412b9270a37ab88e02c1&temp=1754271522", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.317",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.317",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemStoreStaffController#getList(Integer, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.319",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.319",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@d987c6f]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.321",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.329",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754271522", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.330",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.406",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.406",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4b4ad54b]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.409",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.852",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.853",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5b27db]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.854",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.958",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754271522", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.958",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754271522", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.958",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/menu/list?menuType=&name=&temp=1754271522", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.958",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.958",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:42.958",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemMenuController#getList(SystemMenuSearchRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:43.049",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:43.049",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6146dd86]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:43.051",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:43.207",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:43.208",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@56c21a5c]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:43.211",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:43.213",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:43.213",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@1c429ecc]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:43.214",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.490",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getAdminInfoByToken?token=eda45aa88ce8412b9270a37ab88e02c1&temp=1754271527", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.491",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.494",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.495",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@762ff686]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.497",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.504",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/getMenus?temp=1754271527", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.505",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.AdminLoginController#getMenus()" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.765",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.765",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4c369230]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.767",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.819",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/menu/list?menuType=&name=&temp=1754271527", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.819",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754271527", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.819",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/getuniq?key=site_logo_square&temp=1754271527", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.819",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemMenuController#getList(SystemMenuSearchRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.819",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.820",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.900",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.900",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@5ef2050]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.902",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.909",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.909",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@2976480d]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:47.911",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:48.590",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:48.590",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4e5767c1]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:38:48.594",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:18.756",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=20&name=&type=-1&temp=1754271558", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:18.756",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/group/data/list?gid=74&temp=1754271558", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:18.757",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:18.757",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemGroupDataController#getList(SystemGroupDataSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:18.935",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:18.936",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@45a4f425]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:18.939",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:24.364",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:24.364",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3379a1e6]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:24.366",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:47.775",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/store/product/list?page=1&limit=20&keywords=&total=0&isIndex=false&brand=&temp=1754271587", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:47.775",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/brand/list?page=1&limit=10000&type=-1&name=&temp=1754271587", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:47.777",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreBrandController#getBrandList(Integer, String, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:47.777",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.StoreProductController#getList(StoreProductSearchRequest, PageParamRequest)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:52.834",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:52.834",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@3595caf8]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:39:52.838",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:40:08.604",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:40:08.605",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@6b24622d]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:40:08.609",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:41:00.789",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/admin/system/config/info?formId=100&temp=1754271660", parameters={masked}" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:41:00.790",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.genco.admin.controller.SystemConfigController#info(Integer)" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:41:01.050",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:41:01.050",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.genco.common.response.CommonResult@4acb7ea]" }
                    
{
                    "app": "Genco",
                    "timestamp":"2025-08-04 09:41:01.054",
                    "level": "DEBUG",
                    "thread": "http-nio-20000-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
