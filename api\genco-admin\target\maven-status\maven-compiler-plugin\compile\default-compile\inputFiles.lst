C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\manager\CustomAccessDeniedHandler.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\vo\MessageImageItemVo.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\SystemRoleController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\GencoAdminApplication.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\task\coupon\CouponOverdueTask.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\SystemGroupController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\manager\CusAuthenticationManager.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\ShippingTemplatesFreeController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\filter\ResponseFilter.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\StoreProductReplyController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\config\WebConfig.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\ArticleController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\TemplateMessageController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\filter\ResponseRouter.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\CategoryController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\StoreProductController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\SystemCityController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\config\RestTemplateConfig.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\task\pink\PinkStatusChangeTask.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\task\wechat\AsyncWeChatPublicTempMessage.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\StoreOrderStatusController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\SmsRecordController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\UserGroupController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\StoreProductShareRecordController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\service\WeChatMessageService.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\vo\MessageTextVo.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\config\TaskExecutorConfig.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\ValidateCodeController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\SystemUserLevelController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\task\integral\IntegralFrozenTask.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\pub\GetJSConfig.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\acpect\ControllerAspect.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\SystemFormTempController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\filter\JwtAuthenticationTokenFilter.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\task\recharge\RechargePaySuccessTask.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\StoreBrandController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\StoreSeckillController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\StoreCouponUserController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\SystemAttachmentController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\manager\CustomAuthenticationProvider.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\UserLevelController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\config\SchedulerConfig.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\SystemWriteOffOrderController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\StoreOrderController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\vo\MessageVoiceItemVo.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\UserController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\config\SwaggerConfig.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\UserIntegralController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\config\WhatsAppConfig.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\config\JacksonConfig.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\ShippingTemplatesRegionController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\task\brokerage\BrokerageFrozenTask.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\AdminLoginController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\task\product\ProductTikTokAsyncTask.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\vo\MessageReplyDataVo.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\config\CorsConfig.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\StoreBargainController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\SystemGroupDataController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\pub\WeChatPushController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\config\WebSecurityConfig.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\config\DruidConfig.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\vo\BaseMessageVo.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\ExcelController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\task\order\OrderAutoCompleteTask.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\SystemAdminController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\manager\AuthenticationEntryPointImpl.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\service\impl\ValidateCodeServiceImpl.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\UploadController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\RetailShopController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\vo\MessageVoiceVo.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\pub\WeChatMessageController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\task\log\AutoDeleteLogTask.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\task\order\OrderPaySuccessTask.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\ExpressController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\filter\ResponseWrapper.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\task\order\OrderCancelTask.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\CodeGenerator.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\CallbackController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\AffiliateProductController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\service\AdminLoginService.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\service\impl\WhatsAppMessageServiceImpl.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\StoreCouponController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\task\product\ProductStockTask.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\filter\TokenComponent.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\ShippingTemplatesController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\service\impl\WeChatMessageServiceImpl.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\task\order\OrderTiktokPullTask.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\vo\MessageImageVo.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\task\order\OrderAutoCancelTask.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\service\ValidateCodeService.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\vo\ValidateCode.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\UserRechargeController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\SystemStoreController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\pub\ImageMergeController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\UserExtractController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\service\impl\AdminLoginServiceImpl.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\task\order\OrderRefundTask.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\SystemStoreStaffController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\OnePassController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\SystemNotificationController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\task\bargain\BargainStopChangeTask.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\SystemConfigController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\task\order\OrderCompleteTask.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\UserTagController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\task\order\OrderReceiptTask.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\service\impl\VerificationCodeService.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\StoreCombinationController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\service\WhatsAppMessageService.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\task\tiktok\TiktokTokenRefreshTask.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\task\wechat\AsyncWeChatProgramTempMessage.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\StoreProductRuleController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\StoreSeckillMangerController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\SystemMenuController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\FundsMonitorController.java
C:\Users\<USER>\Desktop\新建文件夹 (3)\api\genco-admin\src\main\java\com\genco\admin\controller\HomeController.java
