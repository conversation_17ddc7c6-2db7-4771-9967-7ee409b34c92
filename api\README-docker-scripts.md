# Docker 镜像构建和推送脚本

本项目包含两个自动化脚本来构建和推送 Docker 镜像到 Google Cloud Artifact Registry。

## 脚本说明

### 1. `build-and-push.sh` (完整版)
- 包含错误检查
- 包含 Google Cloud 认证检查
- 包含交互式清理功能
- 彩色日志输出

### 2. `build-and-push-simple.sh` (简化版)
- 基本的构建和推送功能
- 彩色日志输出
- 无交互式功能

## 使用方法

### 前置条件

1. **安装 Docker**
   ```bash
   # 确保 Docker 已安装并运行
   docker --version
   ```

2. **安装 Google Cloud CLI**
   ```bash
   # 安装 gcloud CLI
   # macOS: brew install google-cloud-sdk
   # 其他系统: https://cloud.google.com/sdk/docs/install
   ```

3. **配置 Google Cloud 认证**
   ```bash
   # 登录到 Google Cloud
   gcloud auth login
   
   # 配置 Docker 认证
   gcloud auth configure-docker asia-southeast2-docker.pkg.dev
   ```

### 运行脚本

#### 使用完整版脚本
```bash
./build-and-push.sh
```

#### 使用简化版脚本
```bash
./build-and-push-simple.sh
```

## 脚本功能

脚本会自动执行以下步骤：

1. **检查环境**
   - 验证 Docker 是否运行
   - 验证 Google Cloud 认证状态（仅完整版）

2. **构建镜像**
   ```bash
   docker-compose build
   ```

3. **标记镜像**
   ```bash
   docker tag genco-front:latest asia-southeast2-docker.pkg.dev/testgke-464505/genco-front/genco-front:latest
   docker tag genco-admin:latest asia-southeast2-docker.pkg.dev/testgke-464505/genco-admin/genco-admin:latest
   ```

4. **推送镜像**
   ```bash
   docker push asia-southeast2-docker.pkg.dev/testgke-464505/genco-front/genco-front:latest
   docker push asia-southeast2-docker.pkg.dev/testgke-464505/genco-admin/genco-admin:latest
   ```

## 配置变量

可以在脚本中修改以下配置：

```bash
REGISTRY="asia-southeast2-docker.pkg.dev"
PROJECT_ID="testgke-464505"
FRONT_IMAGE="genco-front"
ADMIN_IMAGE="genco-admin"
TAG="latest"
```

## 输出示例

```
==========================================
    Genco Docker 镜像构建和推送脚本
==========================================
项目ID: testgke-464505
注册表: asia-southeast2-docker.pkg.dev
前端镜像: asia-southeast2-docker.pkg.dev/testgke-464505/genco-front/genco-front:latest
管理端镜像: asia-southeast2-docker.pkg.dev/testgke-464505/genco-admin/genco-admin:latest
==========================================
[INFO] 开始构建 Docker 镜像...
[SUCCESS] Docker 镜像构建完成
[INFO] 标记前端镜像...
[SUCCESS] 前端镜像标记完成
[INFO] 推送前端镜像到 Google Cloud Artifact Registry...
[SUCCESS] 前端镜像推送完成
[INFO] 标记管理端镜像...
[SUCCESS] 管理端镜像标记完成
[INFO] 推送管理端镜像到 Google Cloud Artifact Registry...
[SUCCESS] 管理端镜像推送完成
==========================================
[SUCCESS] 所有镜像构建和推送完成！
==========================================
```

## 故障排除

### 常见问题

1. **Docker 未运行**
   ```
   [ERROR] Docker 未运行，请先启动 Docker
   ```
   解决方案：启动 Docker Desktop 或 Docker 服务

2. **Google Cloud 认证失败**
   ```
   [WARNING] 未检测到活跃的 Google Cloud 认证
   ```
   解决方案：
   ```bash
   gcloud auth login
   gcloud auth configure-docker asia-southeast2-docker.pkg.dev
   ```

3. **推送权限不足**
   ```
   [ERROR] 前端镜像推送失败
   ```
   解决方案：确保有 Google Cloud Artifact Registry 的推送权限

### 手动执行命令

如果脚本失败，可以手动执行以下命令：

```bash
# 构建镜像
docker-compose build

# 标记镜像
docker tag genco-front:latest asia-southeast2-docker.pkg.dev/testgke-464505/genco-front/genco-front:latest
docker tag genco-admin:latest asia-southeast2-docker.pkg.dev/testgke-464505/genco-admin/genco-admin:latest

# 推送镜像
docker push asia-southeast2-docker.pkg.dev/testgke-464505/genco-front/genco-front:latest
docker push asia-southeast2-docker.pkg.dev/testgke-464505/genco-admin/genco-admin:latest
```

## 注意事项

1. 确保在 `genco` 目录下运行脚本
2. 确保 `docker-compose.yaml` 文件存在
3. 确保有足够的磁盘空间用于构建镜像
4. 确保网络连接正常，能够访问 Google Cloud 