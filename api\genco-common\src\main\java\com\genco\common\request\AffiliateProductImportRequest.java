package com.genco.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 联盟商品导入请求
 * 注意：品牌信息现在自动从TikTok API响应中提取，无需手动指定
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AffiliateProductImportRequest对象", description = "联盟商品导入请求")
public class AffiliateProductImportRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商品ID列表", required = true)
    @NotEmpty(message = "商品ID列表不能为空")
    private List<String> productIds;

    @ApiModelProperty(value = "操作用户")
    private String operationUser;
}
