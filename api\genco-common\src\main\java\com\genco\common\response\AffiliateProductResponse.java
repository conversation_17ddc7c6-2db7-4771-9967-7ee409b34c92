package com.genco.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 联盟选品响应对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AffiliateProductResponse", description = "联盟选品响应对象")
public class AffiliateProductResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "产品列表")
    private List<AffiliateProduct> products;

    @ApiModelProperty(value = "下一页游标")
    private String nextPageToken;

    @ApiModelProperty(value = "总数量")
    private Long totalCount;

    /**
     * 联盟产品信息
     */
    @Data
    @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @ApiModel(value = "AffiliateProduct", description = "联盟产品信息")
    public static class AffiliateProduct implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "产品ID")
        private String id;

        @ApiModelProperty(value = "产品标题")
        private String title;

        @ApiModelProperty(value = "主图URL")
        private String mainImageUrl;

        @ApiModelProperty(value = "产品详情链接")
        private String detailLink;

        @ApiModelProperty(value = "是否有库存")
        private Boolean hasInventory;

        @ApiModelProperty(value = "销量")
        private Integer unitsSold;

        @ApiModelProperty(value = "销售区域")
        private String saleRegion;

        @ApiModelProperty(value = "原价信息")
        private PriceInfo originalPrice;

        @ApiModelProperty(value = "销售价格信息")
        private PriceInfo salesPrice;

        @ApiModelProperty(value = "佣金信息")
        private CommissionInfo commission;

        @ApiModelProperty(value = "店铺信息")
        private ShopInfo shop;

        @ApiModelProperty(value = "分类链信息")
        private List<CategoryInfo> categoryChains;

        @ApiModelProperty(value = "是否已入库")
        private Boolean isImported;
    }

    /**
     * 价格信息
     */
    @Data
    @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @ApiModel(value = "PriceInfo", description = "价格信息")
    public static class PriceInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "最低金额")
        private String minimumAmount;

        @ApiModelProperty(value = "最高金额")
        private String maximumAmount;

        @ApiModelProperty(value = "货币单位")
        private String currency;
    }

    /**
     * 佣金信息
     */
    @Data
    @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @ApiModel(value = "CommissionInfo", description = "佣金信息")
    public static class CommissionInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "佣金率（基点，100基点=1%）")
        private Long rate;

        @ApiModelProperty(value = "佣金金额")
        private String amount;

        @ApiModelProperty(value = "货币单位")
        private String currency;
    }

    /**
     * 店铺信息
     */
    @Data
    @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @ApiModel(value = "ShopInfo", description = "店铺信息")
    public static class ShopInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "店铺名称")
        private String name;
    }

    /**
     * 分类信息
     */
    @Data
    @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @ApiModel(value = "CategoryInfo", description = "分类信息")
    public static class CategoryInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "分类ID")
        private String id;

        @ApiModelProperty(value = "分类名称")
        private String localName;

        @ApiModelProperty(value = "是否叶子节点")
        private Boolean isLeaf;

        @ApiModelProperty(value = "父分类ID")
        private String parentId;
    }
}
