package com.genco.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 联盟选品搜索请求对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "AffiliateProductSearchRequest", description = "联盟选品搜索请求对象")
public class AffiliateProductSearchRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "每页数量，默认20，最大100")
    @Min(value = 1, message = "每页数量不能小于1")
    @Max(value = 100, message = "每页数量不能大于100")
    private Long pageSize = 20L;

    @ApiModelProperty(value = "分页游标，用于获取下一页数据")
    private String cursor;

    @ApiModelProperty(value = "排序字段：commission_rate-佣金率，price-价格，sales-销量，created_time-创建时间")
    private String sortField = "commission_rate";

    @ApiModelProperty(value = "排序方向：ASC-升序，DESC-降序")
    private String sortOrder = "DESC";

    @ApiModelProperty(value = "商品标题关键词列表")
    private List<String> titleKeywords;

    @ApiModelProperty(value = "最低销售价格")
    private BigDecimal salesPriceMin;

    @ApiModelProperty(value = "最高销售价格")
    private BigDecimal salesPriceMax;

    @ApiModelProperty(value = "销售价格货币单位，如USD、IDR等")
    private String salesPriceCurrency;

    @ApiModelProperty(value = "分类ID")
    private String categoryId;

    @ApiModelProperty(value = "最低佣金率（基点，100基点=1%）")
    @Min(value = 0, message = "最低佣金率不能小于0")
    private Long commissionRateMin;

    @ApiModelProperty(value = "最高佣金率（基点，100基点=1%）")
    @Max(value = 10000, message = "最高佣金率不能大于10000基点")
    private Long commissionRateMax;

    @ApiModelProperty(value = "是否有库存")
    private Boolean hasInventory;

    @ApiModelProperty(value = "销售区域，如US、ID等")
    private String saleRegion;

    @ApiModelProperty(value = "最低销量")
    @Min(value = 0, message = "最低销量不能小于0")
    private Integer unitsSoldMin;

    @ApiModelProperty(value = "最高销量")
    private Integer unitsSoldMax;
}
