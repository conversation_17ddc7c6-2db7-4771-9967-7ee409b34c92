package com.genco.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.genco.common.model.affiliate.AffiliateProductHistory;
import com.genco.common.page.CommonPage;
import com.genco.common.request.AffiliateProductImportRequest;
import com.genco.common.request.PageParamRequest;
import com.genco.common.response.AffiliateProductImportResponse;
import tiktokshop.open.sdk_java.model.AffiliateCreator.V202501.CreatorSelectAffiliateProductResponseDataProducts;
import tiktokshop.open.sdk_java.model.AffiliateCreator.V202405.CreatorSearchOpenCollaborationProductResponseDataProducts;

import java.util.List;

/**
 * 联盟商品历史记录服务接口
 */
public interface AffiliateProductHistoryService extends IService<AffiliateProductHistory> {

    /**
     * 批量导入联盟商品到本地数据库
     *
     * @param request 导入请求
     * @return 导入结果
     */
    AffiliateProductImportResponse importProducts(AffiliateProductImportRequest request);

    /**
     * 标记商品为已删除状态
     *
     * @param productIds 商品ID列表
     * @param operationUser 操作用户
     * @return 是否成功
     */
    Boolean markProductsAsDeleted(List<String> productIds, String operationUser);

    /**
     * 根据商品ID查询历史记录
     *
     * @param productId 商品ID
     * @return 历史记录
     */
    AffiliateProductHistory getByProductId(String productId);

    /**
     * 分页查询历史记录
     *
     * @param status 状态筛选
     * @param brandId 品牌ID筛选（已废弃，保留参数兼容性）
     * @param pageParamRequest 分页参数
     * @return 分页结果
     */
    CommonPage<AffiliateProductHistory> getHistoryList(Integer status, Integer brandId, PageParamRequest pageParamRequest);

    /**
     * 检查商品是否已存在
     *
     * @param productIds 商品ID列表
     * @return 已存在的商品ID列表
     */
    List<String> checkExistingProducts(List<String> productIds);

    /**
     * 基于TikTok商品数据创建历史记录
     *
     * @param tikTokProduct TikTok商品数据
     */
    void createProductHistoryFromTikTokData(CreatorSearchOpenCollaborationProductResponseDataProducts tikTokProduct);

    /**
     * 基于TikTok商品数据更新历史记录
     *
     * @param tikTokProduct TikTok商品数据
     */
    void updateProductHistoryFromTikTokData(CreatorSearchOpenCollaborationProductResponseDataProducts tikTokProduct);

    /**
     * 更新商品入库状态
     *
     * @param productId 商品ID
     * @param operationUser 操作用户
     * @return 是否成功
     */
    Boolean updateProductImportStatus(String productId, String operationUser);

    /**
     * 更新商品删除状态
     *
     * @param productId 商品ID
     * @param operationUser 操作用户
     * @return 是否成功
     */
    Boolean updateProductDeleteStatus(String productId, String operationUser);
}
